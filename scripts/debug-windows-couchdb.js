#!/usr/bin/env node

/**
 * 🪟 Windows CouchDB Debug Script
 * 
 * Comprehensive debugging tool for Windows CouchDB startup issues
 * Checks paths, binaries, environment, and attempts manual startup
 */

const fs = require('fs');
const path = require('path');
const { spawn, execSync } = require('child_process');

const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');
const COUCHDB_DIR = path.join(RESOURCES_DIR, 'couchdb-windows');

function log(message, color = '\x1b[36m') {
  console.log(`${color}🪟 [Windows Debug] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [Windows Debug] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [Windows Debug] ${message}\x1b[0m`);
}

function logWarning(message) {
  console.log(`\x1b[33m⚠️ [Windows Debug] ${message}\x1b[0m`);
}

function logSection(title) {
  console.log(`\n\x1b[35m=== ${title} ===\x1b[0m`);
}

async function checkWindowsEnvironment() {
  logSection('Windows Environment Check');
  
  log(`Platform: ${process.platform} ${process.arch}`);
  log(`Node.js: ${process.version}`);
  log(`Working Directory: ${process.cwd()}`);
  
  // Check Windows version
  try {
    const winver = execSync('ver', { encoding: 'utf8' });
    log(`Windows Version: ${winver.trim()}`);
  } catch (error) {
    logWarning(`Could not get Windows version: ${error.message}`);
  }
  
  // Check if running as admin
  try {
    execSync('net session', { stdio: 'ignore' });
    logSuccess('Running with administrator privileges');
  } catch (error) {
    logWarning('Not running as administrator');
  }
}

async function checkCouchDBBundle() {
  logSection('CouchDB Bundle Verification');
  
  if (!fs.existsSync(COUCHDB_DIR)) {
    logError(`CouchDB directory not found: ${COUCHDB_DIR}`);
    return false;
  }
  
  logSuccess(`CouchDB directory found: ${COUCHDB_DIR}`);
  
  // Check critical files
  const criticalFiles = [
    'bin/couchdb.cmd',
    'bin/couchjs.exe',
    'etc/default.ini',
    'etc/vm.args',
    'releases/start_erl.data',
    'releases/couchdb.boot',
    'releases/sys.config'
  ];
  
  let allFilesExist = true;
  
  for (const file of criticalFiles) {
    const filePath = path.join(COUCHDB_DIR, file);
    if (fs.existsSync(filePath)) {
      logSuccess(`Found: ${file}`);
    } else {
      logError(`Missing: ${file}`);
      allFilesExist = false;
    }
  }
  
  return allFilesExist;
}

async function checkErlangRuntime() {
  logSection('Erlang Runtime Verification');
  
  // Find ERTS directory
  const entries = fs.readdirSync(COUCHDB_DIR);
  const ertsDir = entries.find(d => d.startsWith('erts-'));
  
  if (!ertsDir) {
    logError('No ERTS directory found');
    logError(`Available directories: ${entries.join(', ')}`);
    return false;
  }
  
  logSuccess(`Found ERTS directory: ${ertsDir}`);
  
  // Check start_erl.data
  const startErlPath = path.join(COUCHDB_DIR, 'releases', 'start_erl.data');
  if (fs.existsSync(startErlPath)) {
    const content = fs.readFileSync(startErlPath, 'utf8').trim();
    const [ertsVersion, couchdbVersion] = content.split(' ');
    log(`start_erl.data: ERTS=${ertsVersion}, CouchDB=${couchdbVersion}`);
    
    const expectedDir = `erts-${ertsVersion}`;
    if (ertsDir === expectedDir) {
      logSuccess('ERTS directory matches start_erl.data');
    } else {
      logWarning(`ERTS directory mismatch: found=${ertsDir}, expected=${expectedDir}`);
    }
  } else {
    logError('start_erl.data not found');
    return false;
  }
  
  // Check Erlang binaries
  const erlangBinDir = path.join(COUCHDB_DIR, ertsDir, 'bin');
  const erlangBinaries = ['erl.exe', 'epmd.exe'];
  
  for (const binary of erlangBinaries) {
    const binaryPath = path.join(erlangBinDir, binary);
    if (fs.existsSync(binaryPath)) {
      logSuccess(`Found Erlang binary: ${binary}`);
    } else {
      logError(`Missing Erlang binary: ${binary}`);
      return false;
    }
  }
  
  return true;
}

async function testBatchFileExecution() {
  logSection('Batch File Test');
  
  const batchFile = path.join(COUCHDB_DIR, 'bin', 'couchdb.cmd');
  
  if (!fs.existsSync(batchFile)) {
    logError('couchdb.cmd not found');
    return false;
  }
  
  log('Testing batch file execution (will timeout after 10 seconds)...');
  
  return new Promise((resolve) => {
    const child = spawn(batchFile, [], {
      cwd: COUCHDB_DIR,
      stdio: 'pipe',
      shell: true
    });
    
    let output = '';
    let hasStarted = false;
    
    child.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(`[STDOUT] ${text.trim()}`);
      
      if (text.includes('Apache CouchDB has started') || 
          text.includes('Time to relax') ||
          text.includes('has started on http://')) {
        hasStarted = true;
        logSuccess('CouchDB started successfully via batch file!');
        child.kill('SIGTERM');
        resolve(true);
      }
    });
    
    child.stderr.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(`[STDERR] ${text.trim()}`);
    });
    
    child.on('close', (code) => {
      if (hasStarted) {
        logSuccess('Batch file test completed successfully');
        resolve(true);
      } else {
        logError(`Batch file exited with code ${code}`);
        if (output) {
          log('Output preview:');
          console.log(output.substring(0, 500) + '...');
        }
        resolve(false);
      }
    });
    
    child.on('error', (error) => {
      logError(`Batch file execution error: ${error.message}`);
      resolve(false);
    });
    
    // Timeout after 10 seconds
    setTimeout(() => {
      if (!hasStarted) {
        child.kill('SIGTERM');
        logWarning('Batch file test timed out (this may be normal)');
        resolve(false);
      }
    }, 10000);
  });
}

async function generateDiagnosticReport() {
  logSection('Generating Diagnostic Report');
  
  const reportPath = path.join(process.cwd(), 'windows-couchdb-diagnostic.txt');
  const timestamp = new Date().toISOString();
  
  const report = [
    `Windows CouchDB Diagnostic Report`,
    `Generated: ${timestamp}`,
    `Platform: ${process.platform} ${process.arch}`,
    `Node.js: ${process.version}`,
    ``,
    `CouchDB Directory: ${COUCHDB_DIR}`,
    `Directory exists: ${fs.existsSync(COUCHDB_DIR)}`,
    ``,
    `Files check:`,
    ...['bin/couchdb.cmd', 'bin/couchjs.exe', 'etc/default.ini', 'etc/vm.args', 'releases/start_erl.data']
      .map(file => `  ${file}: ${fs.existsSync(path.join(COUCHDB_DIR, file)) ? 'EXISTS' : 'MISSING'}`),
    ``,
    `ERTS directories:`,
    ...fs.readdirSync(COUCHDB_DIR).filter(d => d.startsWith('erts-')).map(d => `  ${d}`),
    ``
  ].join('\n');
  
  try {
    fs.writeFileSync(reportPath, report);
    logSuccess(`Diagnostic report saved: ${reportPath}`);
  } catch (error) {
    logError(`Could not save diagnostic report: ${error.message}`);
  }
}

async function main() {
  console.log('\x1b[35m🪟 Windows CouchDB Debug Tool\x1b[0m\n');
  
  if (process.platform !== 'win32') {
    logError('This script is designed for Windows only');
    process.exit(1);
  }
  
  try {
    await checkWindowsEnvironment();
    
    const bundleOk = await checkCouchDBBundle();
    if (!bundleOk) {
      logError('CouchDB bundle verification failed');
      await generateDiagnosticReport();
      process.exit(1);
    }
    
    const erlangOk = await checkErlangRuntime();
    if (!erlangOk) {
      logError('Erlang runtime verification failed');
      await generateDiagnosticReport();
      process.exit(1);
    }
    
    const batchOk = await testBatchFileExecution();
    
    await generateDiagnosticReport();
    
    if (batchOk) {
      logSuccess('🎉 All checks passed! CouchDB should work on Windows.');
    } else {
      logWarning('⚠️ Some issues detected. Check the diagnostic report for details.');
    }
    
  } catch (error) {
    logError(`Debug script failed: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };

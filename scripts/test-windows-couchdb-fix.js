#!/usr/bin/env node

/**
 * 🧪 Windows CouchDB Fix Validation Script
 * 
 * Tests the Windows CouchDB startup fixes by simulating the Electron environment
 * and attempting to start CouchDB using the same logic as the main application
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const http = require('http');

const RESOURCES_DIR = path.join(__dirname, '..', 'electron', 'resources');
const COUCHDB_DIR = path.join(RESOURCES_DIR, 'couchdb-windows');
const TEST_DB_ROOT = path.join(__dirname, '..', 'test-couchdb-data');
const TEST_PORT = 15984; // Use a different port to avoid conflicts

function log(message, color = '\x1b[36m') {
  console.log(`${color}🧪 [Test] ${message}\x1b[0m`);
}

function logSuccess(message) {
  console.log(`\x1b[32m✅ [Test] ${message}\x1b[0m`);
}

function logError(message) {
  console.log(`\x1b[31m❌ [Test] ${message}\x1b[0m`);
}

function logWarning(message) {
  console.log(`\x1b[33m⚠️ [Test] ${message}\x1b[0m`);
}

function logSection(title) {
  console.log(`\n\x1b[35m=== ${title} ===\x1b[0m`);
}

async function setupTestEnvironment() {
  logSection('Test Environment Setup');
  
  // Create test database directory
  if (!fs.existsSync(TEST_DB_ROOT)) {
    fs.mkdirSync(TEST_DB_ROOT, { recursive: true });
    log(`Created test database directory: ${TEST_DB_ROOT}`);
  }
  
  // Create test configuration
  const configDir = path.join(TEST_DB_ROOT, 'couchdb-config');
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  const configPath = path.join(configDir, 'local.ini');
  const configContent = `[couchdb]
database_dir = ${TEST_DB_ROOT}/data
view_index_dir = ${TEST_DB_ROOT}/data
single_node = true

[chttpd]
port = ${TEST_PORT}
bind_address = 0.0.0.0
enable_cors = true

[httpd]
port = ${TEST_PORT}
bind_address = 0.0.0.0

[cors]
enable_cors = true
origins = *
credentials = true
methods = GET, PUT, POST, HEAD, DELETE
headers = accept, authorization, content-type, origin, referer, x-csrf-token

[couch_httpd_auth]
require_valid_user = false

[log]
file = ${TEST_DB_ROOT}/couchdb.log
level = info

[admins]
admin = admin
`;
  
  fs.writeFileSync(configPath, configContent);
  logSuccess(`Created test configuration: ${configPath}`);
  
  return configPath;
}

async function testWindowsCouchDBStartup(configPath) {
  logSection('Windows CouchDB Startup Test');
  
  if (process.platform !== 'win32') {
    logError('This test is designed for Windows only');
    return false;
  }
  
  if (!fs.existsSync(COUCHDB_DIR)) {
    logError(`CouchDB directory not found: ${COUCHDB_DIR}`);
    return false;
  }
  
  // Simulate the fixed Windows startup logic
  const actualCouchdbPath = COUCHDB_DIR;
  
  // Get ERTS version from start_erl.data
  const startErlPath = path.join(actualCouchdbPath, 'releases', 'start_erl.data');
  if (!fs.existsSync(startErlPath)) {
    logError('start_erl.data not found');
    return false;
  }
  
  const startErlContent = fs.readFileSync(startErlPath, 'utf8').trim();
  const expectedErtsVersion = startErlContent.split(' ')[0].trim();
  log(`Using ERTS version: ${expectedErtsVersion}`);
  
  // Construct paths
  const erlangHomeDir = path.join(actualCouchdbPath, `erts-${expectedErtsVersion}`);
  const erlangBinDir = path.join(erlangHomeDir, 'bin');
  const couchBinDir = path.join(actualCouchdbPath, 'bin');
  
  // Verify critical binaries
  const criticalBinaries = [
    path.join(erlangBinDir, 'erl.exe'),
    path.join(erlangBinDir, 'epmd.exe'),
    path.join(couchBinDir, 'couchjs.exe')
  ];
  
  for (const binary of criticalBinaries) {
    if (!fs.existsSync(binary)) {
      logError(`Missing critical binary: ${binary}`);
      return false;
    }
  }
  
  logSuccess('All critical binaries found');
  
  // Setup environment variables
  const spawnEnv = { ...process.env };
  spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
  spawnEnv.ROOTDIR = actualCouchdbPath;
  spawnEnv.COUCHDB_LIB_DIR = path.join(actualCouchdbPath, 'lib');
  spawnEnv.COUCHDB_ETC_DIR = path.join(actualCouchdbPath, 'etc');
  spawnEnv.COUCHDB_QUERY_SERVER_JAVASCRIPT = './bin/couchjs.exe ./share/server/main.js';
  spawnEnv.COUCHDB_QUERY_SERVER_COFFEESCRIPT = './bin/couchjs.exe ./share/server/main-coffee.js';
  spawnEnv.COUCHDB_FAUXTON_DOCROOT = './share/www';
  spawnEnv.ERLANG_HOME = erlangHomeDir;
  spawnEnv.ERTS_VSN = expectedErtsVersion;
  spawnEnv.BINDIR = erlangBinDir;
  spawnEnv.EMU = 'beam';
  spawnEnv.PROGNAME = 'couchdb';
  
  const systemRoot = process.env.SystemRoot || 'C:\\Windows';
  const systemPath = `${couchBinDir};${systemRoot}\\system32;${systemRoot};${systemRoot}\\System32\\Wbem;${systemRoot}\\System32\\WindowsPowerShell\\v1.0\\`;
  spawnEnv.PATH = systemPath;
  
  // Setup spawn arguments
  const spawnArgs = [
    '-boot', path.join(actualCouchdbPath, 'releases', 'couchdb').replace(/\\/g, '/'),
    '-args_file', path.join(actualCouchdbPath, 'etc', 'vm.args').replace(/\\/g, '/'),
    '-epmd', path.join(erlangBinDir, 'epmd.exe').replace(/\\/g, '/'),
    '-config', path.join(actualCouchdbPath, 'releases', 'sys.config').replace(/\\/g, '/'),
    '-couch_ini', path.join(actualCouchdbPath, 'etc', 'default.ini').replace(/\\/g, '/'), configPath.replace(/\\/g, '/')
  ];
  
  const couchDbExecutable = path.join(erlangBinDir, 'erl.exe');
  const spawnOptions = {
    stdio: ['ignore', 'pipe', 'pipe'],
    cwd: actualCouchdbPath,
    shell: false,
    env: spawnEnv
  };
  
  log(`Executable: ${couchDbExecutable}`);
  log(`Working Dir: ${spawnOptions.cwd}`);
  log(`Args: ${spawnArgs.slice(0, 3).join(' ')} ...`);
  
  return new Promise((resolve) => {
    let couchdbProcess;
    
    try {
      couchdbProcess = spawn(couchDbExecutable, spawnArgs, spawnOptions);
      
      if (!couchdbProcess.pid) {
        logError('Failed to spawn CouchDB process - no PID assigned');
        resolve(false);
        return;
      }
      
      logSuccess(`CouchDB process spawned with PID: ${couchdbProcess.pid}`);
      
      let hasStarted = false;
      let output = '';
      
      couchdbProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(`[STDOUT] ${text.trim()}`);
        
        if (text.includes('Apache CouchDB has started') || 
            text.includes('Time to relax') ||
            text.includes('has started on http://')) {
          hasStarted = true;
          logSuccess('CouchDB startup success message detected!');
        }
      });
      
      couchdbProcess.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(`[STDERR] ${text.trim()}`);
      });
      
      couchdbProcess.on('close', (code) => {
        log(`CouchDB process exited with code: ${code}`);
        if (hasStarted) {
          logSuccess('CouchDB started successfully');
          resolve(true);
        } else {
          logError('CouchDB failed to start properly');
          resolve(false);
        }
      });
      
      couchdbProcess.on('error', (error) => {
        logError(`CouchDB process error: ${error.message}`);
        resolve(false);
      });
      
      // Test HTTP connectivity after a delay
      setTimeout(async () => {
        const httpOk = await testHttpConnectivity();
        if (httpOk) {
          logSuccess('HTTP connectivity test passed');
          hasStarted = true;
        }
        
        // Clean up
        if (couchdbProcess && !couchdbProcess.killed) {
          couchdbProcess.kill('SIGTERM');
        }
        
        setTimeout(() => {
          resolve(hasStarted);
        }, 2000);
      }, 8000);
      
    } catch (error) {
      logError(`Error spawning CouchDB: ${error.message}`);
      resolve(false);
    }
  });
}

async function testHttpConnectivity() {
  log('Testing HTTP connectivity...');
  
  return new Promise((resolve) => {
    const req = http.get(`http://localhost:${TEST_PORT}/`, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.couchdb === 'Welcome') {
            logSuccess('CouchDB HTTP endpoint responding correctly');
            resolve(true);
          } else {
            logWarning('CouchDB HTTP endpoint responding but unexpected data');
            resolve(false);
          }
        } catch (error) {
          logWarning('CouchDB HTTP endpoint responding but invalid JSON');
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      log(`HTTP connectivity test failed: ${error.message}`);
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      req.destroy();
      log('HTTP connectivity test timed out');
      resolve(false);
    });
  });
}

async function cleanup() {
  logSection('Cleanup');
  
  try {
    if (fs.existsSync(TEST_DB_ROOT)) {
      fs.rmSync(TEST_DB_ROOT, { recursive: true, force: true });
      log('Cleaned up test database directory');
    }
  } catch (error) {
    logWarning(`Cleanup warning: ${error.message}`);
  }
}

async function main() {
  console.log('\x1b[35m🧪 Windows CouchDB Fix Validation\x1b[0m\n');
  
  if (process.platform !== 'win32') {
    logError('This test is designed for Windows only');
    process.exit(1);
  }
  
  try {
    const configPath = await setupTestEnvironment();
    const success = await testWindowsCouchDBStartup(configPath);
    
    await cleanup();
    
    if (success) {
      logSuccess('🎉 Windows CouchDB fix validation PASSED!');
      logSuccess('The fixes should resolve the production startup issues.');
    } else {
      logError('❌ Windows CouchDB fix validation FAILED!');
      logError('Additional debugging may be required.');
      process.exit(1);
    }
    
  } catch (error) {
    logError(`Test failed: ${error.message}`);
    await cleanup();
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };

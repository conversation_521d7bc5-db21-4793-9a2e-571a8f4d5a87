// P2P Sync module for Electron
// This file handles CouchDB Server, peer discovery via Bonjour, and database syncing

import { BrowserWindow, ipcMain, app } from 'electron';
// Remove express-pouchdb import
// import * as BonjourModule from 'bonjour'; // Changed to namespace import
// Try bonjour-service first, fallback to bonjour
// Try different import patterns to handle module export variations
let BonjourService: any;
try {
  console.log('[p2p-sync.ts] Attempting to import bonjour-service...');
  BonjourService = require('bonjour-service');
  // If it's a module with default export, use that
  if (BonjourService.default && typeof BonjourService.default === 'function') {
    BonjourService = BonjourService.default;
  }
  console.log('[p2p-sync.ts] ✅ Successfully imported bonjour-service');
} catch (importError) {
  console.warn('[p2p-sync.ts] Failed to import bonjour-service, trying bonjour fallback:', importError);
  try {
    BonjourService = require('bonjour');
    console.log('[p2p-sync.ts] ✅ Successfully imported bonjour as fallback');
  } catch (fallbackError) {
    console.error('[p2p-sync.ts] Both bonjour-service and bonjour failed:', fallbackError);
    BonjourService = null;
  }
}
import * as path from 'path';
import * as os from 'os';
import PouchDBConstructor from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';
import * as fs from 'fs';
import * as http from 'http';
import { spawn, ChildProcess } from 'child_process';
import { 
  PREFERRED_COUCHDB_PORT, 
  FALLBACK_COUCHDB_PORTS, 
  ALL_COUCHDB_PORTS,
  getCouchDBUrl,
  getCouchDBAdminUrl 
} from './couchdb-config';

// Setup PouchDB plugins
PouchDBConstructor.plugin(PouchDBFind);

// Add PouchDB namespace for type definitions if needed
declare namespace PouchDB {
  namespace Replication {
    interface ReplicateOptions {
      live?: boolean;
      retry?: boolean;
      filter?: string | ((doc: any, params: any) => boolean);
      query_params?: any;
      view?: string;
      since?: number;
      timeout?: number;
      batch_size?: number;
      batches_limit?: number;
      back_off_function?: (delay: number) => number;
      checkpoint?: false | 'source' | 'target';
    }
  }
}

// Types
interface PeerInfo {
  id: string;
  ip: string;
  port: number;
  hostname: string;
  platform: string;
}

interface SyncStatus {
  peerId: string;
  dbName: string;
  direction: 'push' | 'pull' | 'both';
  status: 'active' | 'paused' | 'error' | 'complete';
  error?: string;
  progress?: {
    docsPushed: number;
    docsPulled: number;
    totalDocs: number;
  };
}

// Service name for Bonjour discovery - MUST use proper mDNS format
const SERVICE_TYPE = '_http._tcp';

// Global state
let couchdbProcess: ChildProcess | null = null;
let bonjour: any = null;
let serverPort: number = PREFERRED_COUCHDB_PORT;
let localDeviceId: string = '';
let mainWindowRef: BrowserWindow | null = null;
let dbRootPathRef: string = '';
let mdnsStatus: 'not_running' | 'running' | 'error' = 'not_running';

// Internet sync state
let internetSyncConfig: {
  enabled: boolean;
  serverUrl: string;
  authToken: string | null;
  registrationInterval: NodeJS.Timeout | null;
} = {
  enabled: false,
  serverUrl: '',
  authToken: null,
  registrationInterval: null
};

// Map to track active sync replications
const activeSyncs: Map<string, any> = new Map();

// Map to track discovered peers
const peers: Map<string, PeerInfo> = new Map();

// Array to track sync statuses
const syncStatuses: SyncStatus[] = [];

const COUCHDB_CONFIG_FILENAME = 'local.ini';
let lastServiceInfo: any = null;

// Utility function to safely send logs to renderer without crashing the main process.
// Added robust guards because in production the BrowserWindow might be reloaded or
// already destroyed while background P2P operations continue emitting logs. Trying
// to access a destroyed webContents throws the fatal "Object has been destroyed"
// error that crashes the whole Electron main process (see issue reports on macOS &
// Windows releases). We therefore:
// 1. Check both the window and its webContents for existence & non-destruction.
// 2. Fallback to console output when the renderer is not available.
function sendP2PLog(message: string) {
  try {
    if (
      mainWindowRef &&
      !mainWindowRef.isDestroyed() &&
      mainWindowRef.webContents &&
      !mainWindowRef.webContents.isDestroyed()
    ) {
      mainWindowRef.webContents.send('p2p-log', message);
    } else {
      // Renderer not ready → simply log to main process console to avoid crashes.
      console.log(`[P2P] ${message}`);
    }
  } catch (error) {
    // Extra safety: never let logging itself crash the app.
    console.warn('[p2p-sync.ts] sendP2PLog failed (safely swallowed):', error);
  }
}

/**
 * Initialize the P2P sync system
 */
export async function initP2PSync(dbRootPath: string, mainWindow: Electron.BrowserWindow, deviceId: string) {
  console.log('[p2p-sync.ts] Attempting to initialize P2P Sync with device ID:', deviceId);
  
  // Store references
  mainWindowRef = mainWindow;
  localDeviceId = deviceId;
  dbRootPathRef = dbRootPath;
  
  // Register IPC handlers FIRST, before any other initialization
  // This ensures they're always available even if other parts fail
  try {
    setupP2PSyncIpcHandlers();
    console.log('[p2p-sync.ts] ✅ P2P Sync IPC handlers registered');
    sendP2PLog(`✅ P2P Sync IPC handlers configured`);
  } catch (error) {
    console.error('[p2p-sync.ts] ❌ Failed to register IPC handlers:', error);
    // Continue anyway, but log the error
  }
  
  // 🚨 CRITICAL FIX: Make P2P sync initialization non-blocking
  // Don't let P2P sync failures prevent the main app from loading
  const initializeP2PAsync = async () => {
    try {
      mdnsStatus = 'not_running';
      // Test the sendP2PLog at the beginning to verify the pipeline
      sendP2PLog(`🔄 Starting P2P Sync initialization with device ID: ${deviceId}`);
      sendP2PLog(`📱 Device ID: ${deviceId}`); // Add explicit device ID log for UI parsing
      
      // Add timeout to CouchDB startup to prevent hanging
      const couchdbStartPromise = startCouchDBServer(dbRootPath);
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('CouchDB startup timeout after 30 seconds')), 30000);
      });
      
      await Promise.race([couchdbStartPromise, timeoutPromise]);
      sendP2PLog(`✅ CouchDB server started successfully on port: ${serverPort}`);
      sendP2PLog(`📡 Service port: ${serverPort}`);
      
      console.log('[p2p-sync.ts] About to start Bonjour discovery...');
      sendP2PLog(`🚀 Starting Bonjour/mDNS discovery system...`);
      
      const bonjourStarted = await startBonjourDiscovery();
      console.log('[p2p-sync.ts] Bonjour discovery result:', bonjourStarted);
      
      if (bonjourStarted) {
        mdnsStatus = 'running';
        console.log('[p2p-sync.ts] ✅ Bonjour discovery started successfully');
        sendP2PLog(`✅ Bonjour discovery started successfully - watching for peers`);
      } else {
        console.log('[p2p-sync.ts] ❌ Bonjour discovery failed to start');
        sendP2PLog(`❌ Bonjour discovery failed to start - check logs for details`);
      }
      
      // Additional diagnostics for debugging
      sendP2PLog(`📊 P2P System State - mdnsStatus: ${mdnsStatus}, peers: ${peers.size}, deviceId: ${deviceId}`);
      
      // Start internet registration if configured
      if (internetSyncConfig.enabled) {
        startInternetRegistration();
      }
      
      console.log(`[p2p-sync.ts] ✅ P2P Sync initialized successfully on port ${serverPort}`);
      sendP2PLog(`✅ P2P Sync initialized successfully on port ${serverPort}`);
      return true;
    } catch (error) {
      console.error('[p2p-sync.ts] ❌ Failed to initialize P2P sync:', error);
      sendP2PLog(`❌ Failed to initialize P2P sync: ${error}`);
      mdnsStatus = 'error';
      // Don't cleanup if only partial initialization failed - keep IPC handlers working
      return false;
    }
  };
  
  // Run P2P initialization in background - don't block main app
  initializeP2PAsync().catch(error => {
    console.error('[p2p-sync.ts] ❌ Background P2P initialization failed:', error);
    sendP2PLog(`❌ Background P2P initialization failed: ${error}`);
  });
  
  // Return immediately with IPC handlers ready
  console.log('[p2p-sync.ts] ✅ P2P Sync IPC ready, initialization continuing in background');
  return true;
}

/**
 * Start the CouchDB Server to expose databases over HTTP
 */
async function startCouchDBServer(dbRootPath: string) {
  console.log('[p2p-sync.ts] Starting CouchDB server...');
  
  try {
    // 🚀 ENHANCEMENT: Different behavior for dev vs prod
    const isDev = process.env.NODE_ENV === 'development' && process.env.ELECTRON_FORCE_STATIC !== 'true';
    
    if (isDev) {
      // 🔧 DEV MODE: Always start fresh, clean up any existing processes
      console.log('[p2p-sync.ts] 🛠️ Development mode: Starting fresh CouchDB instance');
      
      // Kill any existing CouchDB processes that might be running
      try {
        if (process.platform === 'darwin' || process.platform === 'linux') {
          // Kill any existing CouchDB processes
          const { exec } = require('child_process');
          await new Promise<void>((resolve) => {
            exec('pkill -f couchdb', (error: any) => {
              // Ignore errors - process might not exist
              console.log('[p2p-sync.ts] 🧹 Cleaned up any existing CouchDB processes');
              resolve();
            });
          });
        }
        
        // Clean up any existing URI files to prevent conflicts
        const uriFile = path.join(dbRootPath, 'couchdb-config', 'couch.uri');
        if (fs.existsSync(uriFile)) {
          fs.unlinkSync(uriFile);
          console.log('[p2p-sync.ts] 🧹 Removed existing URI file');
        }
        
        // Wait a moment for cleanup
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (cleanupError) {
        console.log('[p2p-sync.ts] Cleanup completed (some operations may have failed, continuing...)');
      }
      
    } else {
      // 🚀 PROD MODE: Check for existing CouchDB instance (existing behavior)
      const uriFile = path.join(dbRootPath, 'couchdb-config', 'couch.uri');
      if (fs.existsSync(uriFile)) {
        try {
          const uriContent = fs.readFileSync(uriFile, 'utf8').trim();
          const portMatch = uriContent.match(/:(\d+)\/$/);
          if (portMatch) {
            const existingPort = parseInt(portMatch[1]);
            console.log(`[p2p-sync.ts] Found existing CouchDB URI file, testing port ${existingPort}...`);
            
            // Test if CouchDB is responsive on this port
            const testResponse = await fetch(`http://localhost:${existingPort}/`, { 
              signal: AbortSignal.timeout(2000) 
            });
            
            if (testResponse.ok) {
              serverPort = existingPort;
              console.log(`[p2p-sync.ts] ✅ Reusing existing CouchDB instance on port ${existingPort}`);
              sendP2PLog(`✅ Connected to existing CouchDB instance on port ${existingPort}`);
              return true;
            }
          }
        } catch (error) {
          console.log(`[p2p-sync.ts] Existing CouchDB not responsive, starting new instance...`);
        }
      }
    }
    
    // Check if CouchDB is already running on expected ports
    let existingPort: number | null = null;
    
    // 🎯 Try preferred port first, then fallbacks
    const portsToTry = [PREFERRED_COUCHDB_PORT, ...FALLBACK_COUCHDB_PORTS];
    
    for (const testPort of portsToTry) {
      try {
        const testResponse = await fetch(`http://localhost:${testPort}/`, {
          signal: AbortSignal.timeout(2000) 
        });
        
        if (testResponse.ok) {
          const responseText = await testResponse.text();
          if (responseText.includes('couchdb')) {
            existingPort = testPort;
            break;
          }
        }
      } catch (error) {
        // Port not available or not CouchDB, try next
      }
    }
    
    if (existingPort) {
      serverPort = existingPort;
      console.log(`[p2p-sync.ts] ✅ Reusing existing CouchDB instance on port ${existingPort}`);
      sendP2PLog(`✅ Connected to existing CouchDB instance on port ${existingPort}`);
      return true;
    }
    
    // 🔍 Find the first available port from our preferred list
    async function findAvailablePort(): Promise<number> {
      // Dynamically import get-port as an ES module at runtime
      // @ts-ignore
      const { default: getPort } = await eval("import('get-port')");
      
      for (const testPort of portsToTry) {
        const availablePort = await getPort({ port: testPort });
        if (availablePort === testPort) {
          return testPort; // Our preferred port is available!
        }
      }
      
      // If none of our preferred ports are available, get any available port
      console.warn('[p2p-sync.ts] ⚠️ All preferred CouchDB ports busy, using random port');
      sendP2PLog('⚠️ All preferred CouchDB ports busy, using random port');
      return await getPort();
    }
    
    serverPort = await findAvailablePort();
    
    console.log(`[p2p-sync.ts] 🚀 Starting new CouchDB instance on port ${serverPort}`);
    sendP2PLog(`🚀 Starting new CouchDB instance on port ${serverPort}`);
    
    // Ensure DB data directory exists
    const couchDbDataPath = path.join(dbRootPath, 'couchdb-data');
    if (!fs.existsSync(couchDbDataPath)) {
      fs.mkdirSync(couchDbDataPath, { recursive: true });
    }
    
    // In packaged apps Electron copies everything specified in `extraResources` to
    // `process.resourcesPath` (e.g. "/Applications/Bistro.app/Contents/Resources").
    // During development we keep binaries under `electron/resources/*` which is
    // resolved via `app.getAppPath() + '/resources'`.

    const resourcesBasePath = app.isPackaged
      ? process.resourcesPath // Production / DMG build
      : path.join(app.getAppPath(), 'resources'); // Dev / live reload

    // ------------------------------------------------------------
    // 🚀 Universal CouchDB path resolution
    // ------------------------------------------------------------
    // Packages sometimes rename / relocate the embedded CouchDB folder.
    // We therefore:
    //  1. Try conventional name based on OS (e.g. couchdb-macos).
    //  2. Scan the resources directory for ANY folder that starts with
    //     "couchdb" (case-insensitive) and pick the first match.
    //  3. Finally, look in `./electron/resources/*` when running unpackaged
    //     in dev mode (already covered via fallbackPath).

    const conventionalDir = process.platform === 'darwin'
      ? 'couchdb-macos'
      : process.platform === 'win32'
        ? 'couchdb-windows'
        : 'couchdb-linux';

    let couchDbPath = path.join(resourcesBasePath, conventionalDir);

    const pathExists = (p: string) => {
      try { return fs.existsSync(p); } catch { return false; }
    };

    if (!pathExists(couchDbPath)) {
      // 🔍 Scan resourcesBasePath for any directory starting with "couchdb"
      const match = fs.readdirSync(resourcesBasePath)
        .find(d => fs.statSync(path.join(resourcesBasePath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchDbPath = path.join(resourcesBasePath, match);
      }
    }

    // Dev fallback (unpacked run)
    if (!pathExists(couchDbPath)) {
      const fallbackPath = path.resolve(__dirname, '../../resources');
      const match = fs.readdirSync(fallbackPath)
        .find(d => fs.statSync(path.join(fallbackPath, d)).isDirectory() && d.toLowerCase().startsWith('couchdb'));
      if (match) {
        couchDbPath = path.join(fallbackPath, match);
      }
    }

    if (!pathExists(couchDbPath)) {
      const err = `❗ Embedded CouchDB directory not found inside resources. Looked for '${conventionalDir}' and scanned for *couchdb* folders.`;
      console.error(`[p2p-sync.ts] ${err}`);
      sendP2PLog(err);
      throw new Error(err);
    }

    // 👉 NEW: On Windows, CouchDB batch scripts fail when ROOTDIR contains spaces (e.g. "Program Files").
    //         To guarantee startup we copy the embedded CouchDB directory once to a path **without spaces**
    //         inside the per-user data folder, then launch CouchDB from there on subsequent runs.
    if (process.platform === 'win32' && couchDbPath.includes(' ')) {
      const portableCouchPath = path.join(app.getPath('userData'), 'couchdb-portable');

      try {
        if (!fs.existsSync(portableCouchPath)) {
          console.log(`[p2p-sync.ts] ⚠️ Detected space in CouchDB path. Copying binaries to safe location: ${portableCouchPath}`);
          sendP2PLog(`⚠️ Copying CouchDB binaries to space-free path…`);

          // Node 16+: fs.cpSync supports recursive directory copy
          fs.cpSync(couchDbPath, portableCouchPath, { recursive: true });
        }

        couchDbPath = portableCouchPath; // Always launch from space-free directory
      } catch (copyErr) {
        console.error('[p2p-sync.ts] ❌ Failed to copy CouchDB binaries to portable location:', copyErr);
        sendP2PLog(`❌ Failed to copy CouchDB binaries: ${copyErr}`);
        // Fallback to original path (may still work on some systems)
      }
    }

    sendP2PLog(`🔧 Using CouchDB path: ${couchDbPath}`);
    console.log(`[p2p-sync.ts] Using CouchDB path: ${couchDbPath}`);
    
    // Create CouchDB config directory
    const couchDbConfigPath = path.join(dbRootPath, 'couchdb-config');
    if (!fs.existsSync(couchDbConfigPath)) {
      fs.mkdirSync(couchDbConfigPath, { recursive: true });
    }
    
    // Create a custom configuration file for CouchDB
    const configFile = path.join(couchDbConfigPath, COUCHDB_CONFIG_FILENAME);
    
    // 🔧 DEV MODE: Use unique node name to prevent conflicts
    const nodeNameSuffix = isDev ? `-dev-${Date.now()}` : '';
    const nodeName = `couchdb@127.0.0.1${nodeNameSuffix}`;
    
    // Basic CouchDB configuration
    const couchDbConfig = `
[couchdb]
uuid = ${localDeviceId}
database_dir = ${couchDbDataPath}
view_index_dir = ${couchDbDataPath}
uri_file = ${path.join(couchDbConfigPath, 'couch.uri')}
single_node = true

[log]
file = ${path.join(dbRootPath, 'couchdb.log')}
level = info

[chttpd]
port = ${serverPort}
bind_address = 0.0.0.0
enable_cors = true

[cors]
origins = *
credentials = true
methods = GET, PUT, POST, HEAD, DELETE
headers = accept, authorization, content-type, origin, referer, x-csrf-token

[couch_httpd_auth]
require_valid_user = false

[admins]
admin = admin

[vm_args]
-name ${nodeName}
    `;
    
    fs.writeFileSync(configFile, couchDbConfig);
    
    // Start CouchDB
    let couchDbExecutable: string;
    
    // ----------------------------------------------------------------
    // 🔑 Executable resolution – support .cmd, .bat, and .exe on Windows
    // ----------------------------------------------------------------
    if (process.platform === 'darwin' || process.platform === 'linux') {
      couchDbExecutable = path.join(couchDbPath, 'bin', 'couchdb');
    } else {
      // try multiple common filenames in order of preference
      const candidates = [
        path.join(couchDbPath, 'bin', 'couchdb.cmd'),
        path.join(couchDbPath, 'bin', 'couchdb.bat'),
        path.join(couchDbPath, 'bin', 'couchdb.exe')
      ];
      couchDbExecutable = candidates.find(pathExists) || candidates[0];
    }
    
    // 🚨 Bullet-proof validation – abort early if executable is missing
    if (!fs.existsSync(couchDbExecutable)) {
      const errMsg = `[p2p-sync.ts] CouchDB executable not found at ${couchDbExecutable}. ` +
                     'The bundled CouchDB resources may be missing or incorrectly packaged.';
      console.error(errMsg);
      sendP2PLog(`❌ ${errMsg}`);
      throw new Error(errMsg);
    }
    
    // Ensure execute permission when possible. On read-only volumes (e.g. DMG)
    // chmod will fail – that is safe to ignore because the packager already
    // made the file executable. We only try to fix perms when writable.
    if (process.platform !== 'win32') {
      try {
        fs.chmodSync(couchDbExecutable, 0o755);
      } catch (permErr: any) {
        if (permErr.code !== 'EROFS' && permErr.code !== 'EPERM') {
          // For unexpected errors, log and re-throw; otherwise continue.
          console.warn('[p2p-sync.ts] chmod failed, continuing anyway:', permErr.message);
        }
      }
    }
    
    // Launch CouchDB process with detailed debug logging
    const stdoutPath = path.join(dbRootPath, 'couchdb-stdout.log');
    const stderrPath = path.join(dbRootPath, 'couchdb-stderr.log');
    
    // Setup environment variables for Windows CouchDB
    let spawnEnv = { ...process.env };
    let spawnArgs: string[] = [];
    let spawnOptions: any = {
      stdio: ['ignore', 'pipe', 'pipe'],
    };
    
    if (process.platform === 'win32') {
       // 🪟 Rock-solid Windows portable CouchDB setup
       // Dynamically locate bundled Erlang dir (erts-*)
       const erlangDirName = fs.readdirSync(couchDbPath).find((d) => d.startsWith('erts-')) || 'erts';
       const erlangHomeDir = path.join(couchDbPath, erlangDirName);
       const erlangBinDir = path.join(erlangHomeDir, 'bin');
       const couchBinDir = path.join(couchDbPath, 'bin');

       // Environment expected by couchdb.cmd
       spawnEnv.ROOTDIR = couchDbPath;
       spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
       spawnEnv.COUCHDB_LIB_DIR = path.join(couchDbPath, 'lib');
       spawnEnv.COUCHDB_ETC_DIR = path.join(couchDbPath, 'etc');
       spawnEnv.COUCHDB_QUERY_SERVER_JAVASCRIPT = './bin/couchjs ./share/server/main.js';
       spawnEnv.COUCHDB_QUERY_SERVER_COFFEESCRIPT = './bin/couchjs ./share/server/main-coffee.js';
       spawnEnv.COUCHDB_FAUXTON_DOCROOT = './share/www';
       spawnEnv.ERLANG_HOME = erlangHomeDir;

       // Ensure Erlang & CouchDB bins are early in PATH
       const currentPath = spawnEnv.PATH || process.env.PATH || '';
       spawnEnv.PATH = `${erlangBinDir};${couchBinDir};${currentPath}`;

       // Pass default.ini THEN our local.ini so overrides win
       spawnArgs = ['-couch_ini', path.join(couchDbPath, 'etc', 'default.ini'), configFile];
       spawnOptions.cwd = couchDbPath;
       spawnOptions.shell = true; // .cmd requires shell on Windows
    } else {
      // 🐧/🍎 Unix-like (macOS & Linux) setup
      spawnEnv.ROOTDIR = couchDbPath;

      // Call script with merged ini list (default first)
      spawnArgs = ['-couch_ini', path.join(couchDbPath, 'etc', 'default.ini'), configFile];
      spawnOptions.cwd = couchDbPath;
    }
    
    spawnOptions.env = spawnEnv;
    
    // Enhanced Windows debugging
    if (process.platform === 'win32') {
      console.log(`[p2p-sync.ts] Windows CouchDB Debug:`);
      console.log(`  - Executable: ${couchDbExecutable}`);
      console.log(`  - Working Dir: ${couchDbPath}`);
      console.log(`  - Args: ${JSON.stringify(spawnArgs)}`);
      console.log(`  - ROOTDIR: ${spawnEnv.ROOTDIR}`);
      console.log(`  - COUCHDB_BIN_DIR: ${spawnEnv.COUCHDB_BIN_DIR}`);
      console.log(`  - PATH: ${spawnEnv.PATH?.split(';').slice(0, 3).join(';')}...`);
      sendP2PLog(`🔧 Starting Windows CouchDB with enhanced environment setup`);
    }
    
    couchdbProcess = spawn(couchDbExecutable, spawnArgs, spawnOptions);
    console.log(`[p2p-sync.ts] Debug: Spawned CouchDB process` +
      ` PID=${couchdbProcess.pid}, cwd=${couchDbPath}, port=${serverPort}, ini=${configFile}`);
    if (couchdbProcess.stdout) {
      couchdbProcess.stdout.on('data', (chunk) => {
        const msg = chunk.toString();
        console.log(`[CouchDB stdout] ${msg.trim()}`);
        fs.appendFileSync(stdoutPath, msg);
      });
    }
    if (couchdbProcess.stderr) {
      couchdbProcess.stderr.on('data', (chunk) => {
        const msg = chunk.toString();
        console.error(`[CouchDB stderr] ${msg.trim()}`);
        fs.appendFileSync(stderrPath, msg);
      });
    }
    // End detailed debug logging
    
    // Handle process events
    couchdbProcess.on('error', (err) => {
      console.error('[p2p-sync.ts] Failed to start CouchDB:', err);
      sendP2PLog(`❌ Failed to start CouchDB: ${err.message}`);
      throw err;
    });
    
    couchdbProcess.on('exit', (code, signal) => {
      if (code !== 0 && code !== null) {
        console.error(`[p2p-sync.ts] CouchDB process exited with code ${code}, signal ${signal}`);
        sendP2PLog(`⚠️ CouchDB process exited with code ${code}`);
      }
    });
    
    // Wait for CouchDB to start with fallback
    try {
      await waitForCouchDB(serverPort);
    } catch (waitError) {
      console.warn(`[p2p-sync.ts] ⚠️ CouchDB health check failed, but trying basic connectivity test...`);
      sendP2PLog(`⚠️ Health check failed, testing basic connectivity...`);
      
      // Fallback: just check if CouchDB responds at all
      try {
        const basicResponse = await fetch(getCouchDBUrl(serverPort), { 
          signal: AbortSignal.timeout(5000) 
        });
        
        if (basicResponse.ok) {
          console.log(`[p2p-sync.ts] ✅ CouchDB responding on basic check, continuing...`);
          sendP2PLog(`✅ CouchDB responding on port ${serverPort} (basic check)`);
        } else {
          throw waitError; // Re-throw original error
        }
      } catch (basicError) {
        console.error(`[p2p-sync.ts] ❌ CouchDB completely unresponsive:`, basicError);
        throw waitError; // Re-throw original error
      }
    }
    
    // 🔧 DEV MODE: Add extra stabilization time for dev environment
    if (isDev) {
      console.log('[p2p-sync.ts] 🛠️ Dev mode: Adding stabilization delay...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    const msg = `🚀 CouchDB server started on port ${serverPort}. Databases in: ${couchDbDataPath}`;
    console.log(msg);
    sendP2PLog(msg);
    return true;
  } catch (error) {
    console.error('[p2p-sync.ts] ❌ Failed to start CouchDB server:', error);
    sendP2PLog(`❌ Failed to start CouchDB server: ${error}`);
    throw error;
  }
}

/**
 * Helper function to wait for CouchDB to be ready
 */
async function waitForCouchDB(port: number, maxRetries = 15, retryDelay = 2000): Promise<void> {
  const checkUrl = getCouchDBUrl(port);
  let retries = 0;
  
  console.log(`[p2p-sync.ts] Waiting for CouchDB on ${checkUrl}`);
  
  while (retries < maxRetries) {
    try {
      // Simple connectivity check with longer timeout
      const response = await fetch(checkUrl, { 
        signal: AbortSignal.timeout(5000),
        headers: { 'Accept': 'application/json' }
      });
      
      if (response.ok) {
        const data = await response.json();
        
        // Check if it looks like CouchDB
        if (data && (data.couchdb || data.uuid)) {
          console.log(`[p2p-sync.ts] ✅ CouchDB is ready on port ${port}:`, data);
          sendP2PLog(`✅ CouchDB ready on port ${port}`);
          return;
        } else {
          throw new Error('Response does not appear to be from CouchDB');
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
    } catch (error: any) {
      const errorMsg = error.name === 'AbortError' || error.name === 'TimeoutError' 
        ? 'Connection timeout' 
        : error.message;
      
      // Log every few attempts
      if (retries % 3 === 0) {
        console.log(`[p2p-sync.ts] CouchDB check ${retries + 1}/${maxRetries}: ${errorMsg}`);
        sendP2PLog(`⏳ Waiting for CouchDB... (${retries + 1}/${maxRetries})`);
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, retryDelay));
    retries++;
  }
  
  throw new Error(`CouchDB failed to start after ${maxRetries} attempts (${maxRetries * retryDelay / 1000}s total)`);
}

/**
 * Verify that our published mDNS service is discoverable
 */
async function verifyServicePublication(serviceName: string, expectedIP: string, expectedPort: number): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!bonjour) {
      reject(new Error('Bonjour instance not available'));
      return;
    }

    const timeout = setTimeout(() => {
      verificationBrowser.stop();
      reject(new Error('Service verification timeout - service not found in 10 seconds'));
    }, 10000);

    const verificationBrowser = bonjour.find({ type: SERVICE_TYPE });
    let serviceFound = false;

    verificationBrowser.on('up', (service: any) => {
      if (service.name === serviceName) {
        serviceFound = true;
        clearTimeout(timeout);
        verificationBrowser.stop();
        
        console.log('[p2p-sync.ts] ✅ Service verification successful:', service.name);
        sendP2PLog(`✅ Verified mDNS service is discoverable: ${serviceName}`);
        sendP2PLog(`✅ Service details: ${service.referer?.address}:${service.port}`);
        resolve();
      }
    });

    // Also check if we find the service after a short delay
    setTimeout(() => {
      if (!serviceFound) {
        console.log('[p2p-sync.ts] ⚠️ Service not yet discovered, continuing verification...');
      }
    }, 3000);
  });
}

/**
 * Start Bonjour service discovery to find other devices
 */
async function startBonjourDiscovery() {
  try {
    console.log('[p2p-sync.ts] ==> startBonjourDiscovery ENTRY POINT');
    sendP2PLog('🚀 Starting Bonjour discovery...');
    
    // CRITICAL: Set service info immediately so UI shows something
    const hostname = os.hostname();
    const networkInterfaces = os.networkInterfaces();
    
    // Find a suitable IP address first (for logging and fallback)
    let localIp = '127.0.0.1';
    const validIPs: string[] = [];
    const interfaceDetails: any[] = [];
    
    for (const interfaceName in networkInterfaces) {
      const addresses = networkInterfaces[interfaceName];
      if (addresses) {
        for (const address of addresses) {
          if (!address.internal && address.family === 'IPv4') {
            validIPs.push(address.address);
            interfaceDetails.push({
              interface: interfaceName,
              address: address.address,
              netmask: address.netmask,
              mac: address.mac
            });
          }
        }
      }
    }
    
    const preferredIP = validIPs.find(ip => 
      ip.startsWith('192.168.') || 
      ip.startsWith('10.') || 
      ip.startsWith('172.')
    ) || validIPs[0];
    
    if (preferredIP) {
      localIp = preferredIP;
    }
    
    // CRITICAL FIX: Use hostname.local format for better external discovery
    const hostnameDotLocal = `${hostname}.local`;
    const serviceName = `PouchDB-${hostname}-${localDeviceId.substring(0, 8)}`;
    
    console.log('[p2p-sync.ts] 🌐 Network interface analysis:');
    console.log('[p2p-sync.ts] - Hostname:', hostname);
    console.log('[p2p-sync.ts] - Hostname.local:', hostnameDotLocal);
    console.log('[p2p-sync.ts] - Preferred IP:', localIp);
    console.log('[p2p-sync.ts] - All valid IPs:', validIPs);
    console.log('[p2p-sync.ts] - Interface details:', JSON.stringify(interfaceDetails, null, 2));
    
    sendP2PLog(`🌐 Network analysis: hostname=${hostnameDotLocal}, IP=${localIp}`);
    sendP2PLog(`🌐 Available interfaces: ${interfaceDetails.map(i => `${i.interface}(${i.address})`).join(', ')}`);
    
    // SET SERVICE INFO IMMEDIATELY - BEFORE ANY BONJOUR OPERATIONS
    lastServiceInfo = {
      serviceName: serviceName,
      ip: localIp,
      port: serverPort,
      serviceType: SERVICE_TYPE,
      domain: 'local.',
      deviceId: localDeviceId,
      platform: 'desktop',
      txt: {
        id: localDeviceId,
        version: '1.0',
        platform: 'desktop',
        deviceId: localDeviceId,
        path: '/'
      },
      publishStatus: 'attempting',
      lastCheckpoint: 'initial'
    };
    
    console.log('[p2p-sync.ts] ✅ Service info set immediately for UI:', JSON.stringify(lastServiceInfo, null, 2));
    sendP2PLog(`📋 Service info ready: ${serviceName} at ${localIp}:${serverPort}`);
    
    console.log('[p2p-sync.ts] ==> CHECKPOINT 1: Service info set');
    sendP2PLog('✅ CHECKPOINT 1: Service info configured');
    lastServiceInfo.lastCheckpoint = 'service_info_set';
    
    console.log('[p2p-sync.ts] ==> CHECKPOINT 2: About to check BonjourService');
    sendP2PLog('🔍 CHECKPOINT 2: Checking bonjour module...');
    lastServiceInfo.lastCheckpoint = 'checking_module';
    
    // Check if Bonjour module is available
    if (!BonjourService) {
      console.error('[p2p-sync.ts] ❌ BonjourService module not available!');
      sendP2PLog(`❌ Bonjour module not loaded - mDNS will not work`);
      lastServiceInfo.publishStatus = 'module_error';
      lastServiceInfo.publishError = 'Bonjour module not available';
      mdnsStatus = 'error';
      return false;
    }
    
    console.log('[p2p-sync.ts] ==> CHECKPOINT 3: BonjourService available');
    console.log('[p2p-sync.ts] BonjourService module type:', typeof BonjourService);
    sendP2PLog('✅ CHECKPOINT 3: BonjourService module available');
    lastServiceInfo.lastCheckpoint = 'module_available';
    console.log('[p2p-sync.ts] BonjourService available, proceeding with initialization');
    
    console.log('[p2p-sync.ts] ==> CHECKPOINT 4: About to create bonjour instance');
    sendP2PLog('🔍 CHECKPOINT 4: Creating bonjour instance...');
    lastServiceInfo.lastCheckpoint = 'creating_instance';
    
    // Enhanced bonjour initialization with detailed debugging
    try {
      // Try different ways to instantiate bonjour-service
      console.log('[p2p-sync.ts] BonjourService type:', typeof BonjourService);
      console.log('[p2p-sync.ts] BonjourService string:', BonjourService.toString().substring(0, 100));
      
      if (typeof BonjourService === 'function') {
        try {
          // Try as constructor first
          bonjour = new BonjourService();
          console.log('[p2p-sync.ts] ✅ Created with new BonjourService()');
        } catch (newError: any) {
          console.log('[p2p-sync.ts] new failed:', newError.message);
          try {
            // Try as function call
            bonjour = BonjourService();
            console.log('[p2p-sync.ts] ✅ Created with BonjourService()');
          } catch (callError: any) {
            console.log('[p2p-sync.ts] function call failed:', callError.message);
            throw new Error(`Both new and function call failed: ${newError.message}, ${callError.message}`);
          }
        }
      } else {
        throw new Error(`BonjourService is not a function, type: ${typeof BonjourService}`);
      }
    } catch (bonjourError: any) {
      console.error('[p2p-sync.ts] Failed to initialize Bonjour module:', bonjourError);
      sendP2PLog(`❌ Failed to initialize Bonjour module: ${bonjourError}`);
      sendP2PLog(`❌ BonjourService type: ${typeof BonjourService}`);
      sendP2PLog(`❌ BonjourService keys: ${BonjourService ? Object.keys(BonjourService).join(', ') : 'none'}`);
      
      lastServiceInfo.publishStatus = 'bonjour_error';
      lastServiceInfo.publishError = bonjourError.message;
      mdnsStatus = 'error';
      return false;
    }
    
    if (!bonjour) {
      console.error('[p2p-sync.ts] Failed to initialize Bonjour instance!');
      mdnsStatus = 'error';
      sendP2PLog(`❌ Failed to initialize Bonjour instance - bonjour is null`);
      
      lastServiceInfo.publishStatus = 'instance_error';
      lastServiceInfo.publishError = 'Bonjour instance is null after initialization';
      return false;
    }

    console.log('[p2p-sync.ts] ==> CHECKPOINT 5: Bonjour instance created successfully');
    console.log('[p2p-sync.ts] ✅ Bonjour instance created successfully');
    console.log('[p2p-sync.ts] Bonjour instance methods:', {
      publish: typeof bonjour.publish,
      find: typeof bonjour.find,
      destroy: typeof bonjour.destroy
    });
    
    sendP2PLog(`✅ CHECKPOINT 5: Bonjour instance ready - publish: ${typeof bonjour.publish}, find: ${typeof bonjour.find}`);
    lastServiceInfo.lastCheckpoint = 'instance_ready';
    
    // IP and hostname already determined above
    console.log(`[p2p-sync.ts] Using pre-determined IP: ${localIp} and serviceName: ${serviceName}`);
    sendP2PLog(`🌐 Selected network IP: ${localIp}`);
    sendP2PLog(`🌐 Available IPs: ${validIPs.join(', ')}`);
    
    // CRITICAL FIX: Bonjour-service expects different configuration format
    // Remove 'protocol' and 'host' - these cause issues with bonjour-service
    const serviceInfo = {
      name: serviceName,
      type: SERVICE_TYPE,  // '_http._tcp'
      port: serverPort,
      txt: {
        id: localDeviceId,
        version: '1.0',
        platform: 'desktop',
        deviceId: localDeviceId,
        path: '/'
      }
    };
    
    console.log('[p2p-sync.ts] 🔧 SIMPLIFIED service config (removed protocol/host):', JSON.stringify(serviceInfo, null, 2));
    
    console.log('[p2p-sync.ts] Publishing service with config:', JSON.stringify(serviceInfo, null, 2));
    
    // Service info now stored before publishing attempt
    
    // Store the latest service info BEFORE publishing (so UI can show the attempt)
    lastServiceInfo = { 
      ...serviceInfo, 
      ip: localIp,
      serviceName: serviceName,
      serviceType: SERVICE_TYPE,
      domain: 'local.'
    };
    
    console.log('[p2p-sync.ts] Stored service info for UI:', JSON.stringify(lastServiceInfo, null, 2));
    
    console.log('[p2p-sync.ts] ==> CHECKPOINT 6: About to publish service');
    sendP2PLog('🚀 CHECKPOINT 6: Starting service publish...');
    lastServiceInfo.lastCheckpoint = 'about_to_publish';
    
    // Publish our service with enhanced error handling
    let service;
    try {
      console.log(`[p2p-sync.ts] Publishing mDNS service: ${serviceName} on ${localIp}:${serverPort}`);
      sendP2PLog(`🔄 Attempting to publish mDNS service...`);
      
      // Add synchronous error checking
      if (!bonjour || typeof bonjour.publish !== 'function') {
        throw new Error('Bonjour instance invalid or publish method missing');
      }
      
      // CRITICAL: Use native dns-sd for reliable external discovery
      console.log('[p2p-sync.ts] 🚀 Using native dns-sd for guaranteed external discovery...');
      sendP2PLog(`🚀 Using native dns-sd for reliable external discovery...`);

      // First, try bonjour-service for internal discovery (peer-to-peer within app)
      console.log('[p2p-sync.ts] Step 1: Setting up bonjour-service for internal discovery...');
      const configWithHostnameLocal = {
        ...serviceInfo,
        host: hostnameDotLocal,
        domain: 'local.'
      };
      let service: any = bonjour.publish(configWithHostnameLocal);

      if (!service) {
        // Try fallback config
        service = bonjour.publish(serviceInfo);
      }

      // Step 2: ALWAYS use native dns-sd for external discovery (this is what works!)
      console.log('[p2p-sync.ts] Step 2: Starting native dns-sd for external discovery...');
      sendP2PLog(`📡 Starting native dns-sd registration...`);

      const txtRecord = `id=${localDeviceId} version=1.0 platform=desktop path=/`;

      // Use dns-sd command to register service for external discovery
      const dnssdProcess = spawn('dns-sd', [
        '-R',
        serviceName,
        '_http._tcp',
        'local',
        serverPort.toString(),
        txtRecord
      ], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      dnssdProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString().trim();
        console.log('[dns-sd stdout]', output);
        sendP2PLog(`📡 dns-sd: ${output}`);

        // Check for successful registration
        if (output.includes('Name now registered and active')) {
          sendP2PLog(`✅ 📡 EXTERNAL DISCOVERY ACTIVE!`);
          console.log('[p2p-sync.ts] ✅ 📡 EXTERNAL DISCOVERY CONFIRMED ACTIVE!');
        }
      });

      dnssdProcess.stderr?.on('data', (data: Buffer) => {
        const error = data.toString().trim();
        console.error('[dns-sd stderr]', error);
        sendP2PLog(`⚠️ dns-sd: ${error}`);
      });

      dnssdProcess.on('error', (error: Error) => {
        console.error('[p2p-sync.ts] dns-sd process error:', error);
        sendP2PLog(`❌ dns-sd process failed: ${error.message}`);
      });

      // Store process reference for cleanup
      (global as any).dnssdProcess = dnssdProcess;

      sendP2PLog(`🚀 Native dns-sd registration started for ${serviceName}`);
      console.log('[p2p-sync.ts] ✅ Native dns-sd process started for external discovery');
      
      if (service) {
        console.log('[p2p-sync.ts] ✅ Service published successfully!');
        console.log('[p2p-sync.ts] Service object type:', typeof service);
        console.log('[p2p-sync.ts] Service object properties:', Object.getOwnPropertyNames(service));

        // Determine which configuration worked
        let workingConfig = 'unknown';
        if (service.host === hostnameDotLocal) {
          workingConfig = 'hostname.local (BEST for external discovery)';
        } else if (service.host === localIp) {
          workingConfig = 'IP address host';
        } else if (!service.host) {
          workingConfig = 'no host parameter';
        }

        sendP2PLog(`✅ mDNS service published: ${serviceName}`);
        sendP2PLog(`📡 Working config: ${workingConfig}`);
        sendP2PLog(`📡 Service type: ${SERVICE_TYPE}`);
        sendP2PLog(`📡 Service name: ${serviceName}`);
        sendP2PLog(`📡 Service port: ${serverPort}`);
        sendP2PLog(`📡 Host parameter: ${service.host || 'none'}`);
        sendP2PLog(`📡 Local IP: ${localIp}`);
        sendP2PLog(`📡 Hostname.local: ${hostnameDotLocal}`);
        sendP2PLog(`📡 TXT records: ${JSON.stringify(serviceInfo.txt)}`);

        // CRITICAL: Check service state (handle both bonjour-service and native)
        if ((service as any).native) {
          console.log('[p2p-sync.ts] 🔍 Native dns-sd service - external discovery guaranteed');
          sendP2PLog(`🔍 Native dns-sd service active - external discovery guaranteed`);
        } else {
          console.log('[p2p-sync.ts] 🔍 Bonjour-service state analysis:');
          console.log('[p2p-sync.ts] - service.published:', (service as any).published);
          console.log('[p2p-sync.ts] - service.activated:', (service as any).activated);
          console.log('[p2p-sync.ts] - service.destroyed:', (service as any).destroyed);
          console.log('[p2p-sync.ts] - service.fqdn:', (service as any).fqdn);

          sendP2PLog(`🔍 Bonjour service state: published=${(service as any).published}, activated=${(service as any).activated}`);
          sendP2PLog(`🔍 Service FQDN: ${(service as any).fqdn || 'none'}`);

          // Explicitly start the service if it's not activated
          if (!(service as any).activated && typeof (service as any).start === 'function') {
            console.log('[p2p-sync.ts] 🚀 Explicitly starting bonjour service...');
            sendP2PLog(`🚀 Explicitly starting bonjour service activation...`);
            try {
              (service as any).start();
              console.log('[p2p-sync.ts] ✅ Bonjour service start() called successfully');
              sendP2PLog(`✅ Bonjour service start() called - checking activation...`);

              // Check activation after a brief delay
              setTimeout(() => {
                console.log('[p2p-sync.ts] 🔍 Post-start bonjour service state:');
                console.log('[p2p-sync.ts] - service.published:', (service as any).published);
                console.log('[p2p-sync.ts] - service.activated:', (service as any).activated);
                sendP2PLog(`🔍 Post-start: published=${(service as any).published}, activated=${(service as any).activated}`);
              }, 1000);
            } catch (startError) {
              console.error('[p2p-sync.ts] ❌ Failed to start bonjour service:', startError);
              sendP2PLog(`❌ Failed to start bonjour service: ${startError}`);
            }
          }
        }

        // Update service info with success status
        if ((service as any).native) {
          lastServiceInfo.publishStatus = 'native_dns_sd';
          sendP2PLog(`✅ Hybrid approach: Bonjour-service + Native dns-sd for maximum compatibility`);
        } else {
          lastServiceInfo.publishStatus = 'success';
        }
        
        // CRITICAL: Force immediate advertisement
        if (service && typeof service.start === 'function') {
          console.log('[p2p-sync.ts] 🚀 Starting service advertisement...');
          service.start();
          sendP2PLog(`🚀 Service advertisement started`);
        }
        
        // Wait a moment then verify the service is actually broadcasting
        setTimeout(() => {
          console.log('[p2p-sync.ts] 🔍 Verifying service is broadcasting...');
          
          // Use dns-sd command line tool to verify (macOS/Linux)
          if (process.platform === 'darwin' || process.platform === 'linux') {
            const { exec } = require('child_process');
            exec(`dns-sd -B _http._tcp`, { timeout: 3000 }, (error: any, stdout: string) => {
              if (error) {
                console.log('[p2p-sync.ts] ⚠️ dns-sd verification failed:', error.message);
                sendP2PLog(`⚠️ dns-sd verification failed: ${error.message}`);
              } else {
                console.log('[p2p-sync.ts] dns-sd output:', stdout);
                if (stdout.includes(serviceName)) {
                  sendP2PLog(`✅ Service verified via dns-sd: ${serviceName}`);
                } else {
                  sendP2PLog(`⚠️ Service not found in dns-sd output`);
                }
              }
            });
          }
        }, 2000);
        
        // Test if service is actually accessible
        setTimeout(() => {
          console.log('[p2p-sync.ts] Testing service publication...');
          sendP2PLog(`🔍 Testing if service is discoverable...`);
          
          // Try to verify our own service exists
          const testBrowser = bonjour.find({ type: SERVICE_TYPE });
          let foundOurService = false;
          
          testBrowser.on('up', (foundService: any) => {
            if (foundService.name === serviceName) {
              foundOurService = true;
              console.log('[p2p-sync.ts] ✅ Our service is discoverable!');
              sendP2PLog(`✅ Service verified: ${serviceName} is discoverable`);
              testBrowser.stop();
            }
          });
          
          setTimeout(() => {
            if (!foundOurService) {
              console.log('[p2p-sync.ts] ⚠️ Our service not found via discovery - may be a network issue');
              sendP2PLog(`⚠️ Service not found via discovery - check firewall/network`);
            }
            testBrowser.stop();
          }, 3000);
        }, 1000);
        
      } else {
        console.error('[p2p-sync.ts] ❌ Bonjour-service failed, but native dns-sd should still work');
        sendP2PLog(`⚠️ Bonjour-service failed, relying on native dns-sd for discovery`);

        // Create a minimal service object for compatibility
        service = { native: true, published: true, activated: true };
      }
    } catch (publishError: any) {
      console.error('[p2p-sync.ts] ❌ Failed to publish service:', publishError);
      sendP2PLog(`❌ Failed to publish mDNS service: ${publishError}`);
      sendP2PLog(`❌ Service config: ${JSON.stringify(serviceInfo, null, 2)}`);
      sendP2PLog(`❌ Bonjour instance type: ${typeof bonjour}`);
      sendP2PLog(`❌ Bonjour publish method: ${typeof bonjour?.publish}`);
      
      // Update service info with error status but keep the info for debugging
      if (lastServiceInfo) {
        lastServiceInfo.publishStatus = 'error';
        lastServiceInfo.publishError = publishError.message;
      }
      
      mdnsStatus = 'error';
      return false;
    }
    
    // Browser for other services with proper type
    let browser;
    try {
      console.log(`[p2p-sync.ts] Creating service browser for type: ${SERVICE_TYPE}`);
      browser = bonjour.find({ type: SERVICE_TYPE });
      console.log('[p2p-sync.ts] ✅ Service browser created successfully');
      sendP2PLog(`✅ Service browser created for type: ${SERVICE_TYPE}`);
    } catch (browserError) {
      console.error('[p2p-sync.ts] ❌ Failed to create service browser:', browserError);
      sendP2PLog(`❌ Failed to create service browser: ${browserError}`);
      // Continue anyway - publishing might still work
    }
    
    if (browser) {
      // When we find a service
      browser.on('up', (service: any) => {
        // Skip our own service
        if (service.txt.id === localDeviceId) return;
        
        sendP2PLog(`👂 Bonjour event: action=up, service=${service.name}`);
        
        // Detailed service logging
        try {
          const safeServiceForLog = {
            name: service.name,
            type: service.type,
            protocol: service.protocol,
            port: service.port,
            txt: service.txt || {},
            addresses: service.addresses || [],
            referer: service.referer || {}
          };
          sendP2PLog(`🔍 Raw Bonjour service data: ${JSON.stringify(safeServiceForLog)}`);
        } catch (error) {
          console.error('Error logging service object:', error);
        }
        
        sendP2PLog(`🔍 Discovered peer: ${service.name}`);
        // Create peer info object with fields compatible with mobile implementation
        const peerInfo: PeerInfo = {
          id: service.txt.id,
          ip: service.referer.address,
          port: service.port,
          hostname: service.name,
          platform: service.txt.platform || 'unknown'
        };
        
        // Extra logging for IP address resolution
        sendP2PLog(`🔍 Detected IP address for peer: ${peerInfo.ip}, port: ${peerInfo.port}`);
        
        // Verify we have a valid IP address
        if (!peerInfo.ip || peerInfo.ip === '0.0.0.0' || peerInfo.ip === '127.0.0.1') {
          // Try to use addresses array if referer address is not usable
          if (service.addresses && service.addresses.length > 0) {
            // Filter for IPv4 addresses that are not localhost
            const validIPs = service.addresses.filter((addr: string) => {
              return addr.indexOf(':') === -1 && !addr.startsWith('127.') && !addr.startsWith('169.254');
            });
            
            if (validIPs.length > 0) {
              peerInfo.ip = validIPs[0];
              sendP2PLog(`🔄 Updated peer IP from addresses array: ${peerInfo.ip}`);
            }
          }
        }
        
        // Debug output for platform detection
        sendP2PLog(`🔍 Detected platform for peer: ${peerInfo.platform}`);
        
        // Store the peer
        peers.set(peerInfo.id, peerInfo);
        // Ensure mDNS status is set to running when peers are discovered
        if (mdnsStatus !== 'running') {
          mdnsStatus = 'running';
        }
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('peer-discovered', peerInfo);
        }
      });
      
      // When a service goes down
      browser.on('down', (service: any) => {
        // Skip our own service
        if (service.txt.id === localDeviceId) return;
        sendP2PLog(`👂 Bonjour event: action=down, service=${service.name}`);
        sendP2PLog(`👋 Lost peer: ${service.name}`);
        const peerId = service.txt.id;
        // Stop any active syncs with this peer
        stopAllSyncsWithPeer(peerId);
        // Remove from peers map
        peers.delete(peerId);
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('peer-lost', peerId);
        }
      });
    }
    
    // Verify our service is discoverable by trying to find ourselves
    setTimeout(async () => {
      try {
        await verifyServicePublication(serviceName, localIp, serverPort);
      } catch (error) {
        console.error('[p2p-sync.ts] Service verification failed:', error);
        sendP2PLog(`⚠️ Service verification failed: ${error}`);
      }
    }, 2000); // Wait 2 seconds for service to propagate
    
    mdnsStatus = 'running';
    console.log('[p2p-sync.ts] ✅ Bonjour discovery setup complete');
    sendP2PLog(`✅ mDNS service broadcasting on ${localIp}:${serverPort}`);
    
    // Enhanced external verification with multiple methods
    setTimeout(() => {
      console.log('[p2p-sync.ts] 🔍 Enhanced external broadcast verification...');
      sendP2PLog(`🔍 Verifying external mDNS discoverability...`);

      if (process.platform === 'darwin' || process.platform === 'linux') {
        const { exec } = require('child_process');

        // Method 1: Browse for services
        console.log('[p2p-sync.ts] Method 1: Browsing for _http._tcp services...');
        exec(`dns-sd -B _http._tcp local.`, { timeout: 8000 }, (error: any, stdout: string) => {
          if (error) {
            console.log('[p2p-sync.ts] ⚠️ Browse verification failed:', error.message);
            sendP2PLog(`⚠️ Browse verification failed: ${error.message}`);
          } else {
            console.log('[p2p-sync.ts] Current mDNS broadcasts:', stdout);
            if (stdout.includes(serviceName) || stdout.includes('PouchDB-')) {
              sendP2PLog(`✅ 📡 SERVICE EXTERNALLY DISCOVERABLE!`);
              console.log('[p2p-sync.ts] ✅ 📡 SERVICE EXTERNALLY DISCOVERABLE!');
            } else {
              sendP2PLog(`❌ Service not found in external mDNS broadcasts`);
              console.log('[p2p-sync.ts] ❌ Service not externally discoverable');

              // Method 2: Try to resolve our specific service
              console.log('[p2p-sync.ts] Method 2: Attempting direct service resolution...');
              exec(`dns-sd -L "${serviceName}" _http._tcp local.`, { timeout: 5000 }, (resolveError: any, resolveStdout: string) => {
                if (resolveError) {
                  sendP2PLog(`❌ Direct resolution failed: ${resolveError.message}`);
                } else if (resolveStdout.includes(serviceName)) {
                  sendP2PLog(`✅ Service resolvable but not browsable - partial success`);
                } else {
                  sendP2PLog(`❌ Service not resolvable - check network/firewall/host config`);
                }
              });
            }
          }
        });

        // Method 3: Check if our hostname.local is resolvable
        setTimeout(() => {
          console.log('[p2p-sync.ts] Method 3: Testing hostname.local resolution...');
          exec(`ping -c 1 ${hostnameDotLocal}`, { timeout: 3000 }, (pingError: any, pingStdout: string) => {
            if (pingError) {
              sendP2PLog(`⚠️ ${hostnameDotLocal} not resolvable - this may affect discovery`);
            } else {
              sendP2PLog(`✅ ${hostnameDotLocal} is resolvable`);
            }
          });
        }, 1000);
      } else {
        sendP2PLog(`⚠️ External verification not available on Windows - use Bonjour Browser app`);
      }
    }, 3000);
    
    return true;
  } catch (error) {
    console.error('❌ Failed to start Bonjour discovery:', error);
    sendP2PLog(`❌ Failed to start Bonjour discovery: ${error}`);
    mdnsStatus = 'error';
    return false;
  }
}

/**
 * Configure internet sync registration
 */
export function configureInternetSync(config: {
  enabled: boolean;
  serverUrl: string;
  authToken: string;
}) {
  internetSyncConfig.enabled = config.enabled;
  internetSyncConfig.serverUrl = config.serverUrl;
  internetSyncConfig.authToken = config.authToken;
  
  sendP2PLog(`🌐 Internet sync configured: ${config.enabled ? 'enabled' : 'disabled'}`);
  
  if (config.enabled && serverPort && localDeviceId) {
    startInternetRegistration();
  } else if (!config.enabled && internetSyncConfig.registrationInterval) {
    stopInternetRegistration();
  }
}

/**
 * Start internet registration with the server
 */
function startInternetRegistration() {
  if (!internetSyncConfig.enabled || !internetSyncConfig.serverUrl || !internetSyncConfig.authToken) {
    return;
  }
  
  // Clear existing interval
  if (internetSyncConfig.registrationInterval) {
    clearInterval(internetSyncConfig.registrationInterval);
  }
  
  sendP2PLog(`🌐 Starting internet registration`);
  
  // Initial registration
  registerWithInternetServer();
  
  // Set up periodic heartbeat (every 60 seconds)
  internetSyncConfig.registrationInterval = setInterval(() => {
    sendHeartbeatToInternetServer();
  }, 60000);
}

/**
 * Stop internet registration
 */
function stopInternetRegistration() {
  if (internetSyncConfig.registrationInterval) {
    clearInterval(internetSyncConfig.registrationInterval);
    internetSyncConfig.registrationInterval = null;
  }
  
  // Unregister device
  if (internetSyncConfig.enabled && internetSyncConfig.serverUrl && internetSyncConfig.authToken) {
    unregisterFromInternetServer();
  }
  
  sendP2PLog(`🌐 Stopped internet registration`);
}

/**
 * Register device with internet server
 */
async function registerWithInternetServer() {
  try {
    // Get local IP address
    const networkInterfaces = os.networkInterfaces();
    let localIp = '127.0.0.1';
    
    for (const interfaceName in networkInterfaces) {
      const addresses = networkInterfaces[interfaceName];
      if (addresses) {
        for (const address of addresses) {
          if (!address.internal && address.family === 'IPv4') {
            localIp = address.address;
            break;
          }
        }
      }
    }
    
    const response = await fetch(`${internetSyncConfig.serverUrl}/api/sync/register-device`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${internetSyncConfig.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        deviceId: localDeviceId,
        deviceType: 'desktop',
        couchdbPort: serverPort,
        ipAddress: localIp
      })
    });
    
    if (response.ok) {
      sendP2PLog(`🌐 ✅ Successfully registered with internet server`);
    } else {
      const errorText = await response.text();
      sendP2PLog(`🌐 ❌ Registration failed: ${response.status} ${errorText}`);
    }
    
  } catch (error) {
    sendP2PLog(`🌐 ❌ Registration error: ${(error as Error).message}`);
  }
}

/**
 * Send heartbeat to internet server
 */
async function sendHeartbeatToInternetServer() {
  try {
    const response = await fetch(`${internetSyncConfig.serverUrl}/api/sync/heartbeat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${internetSyncConfig.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        deviceId: localDeviceId
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      sendP2PLog(`🌐 ⚠️ Heartbeat failed: ${response.status} ${errorText}`);
    }
    
  } catch (error) {
    sendP2PLog(`🌐 ⚠️ Heartbeat error: ${(error as Error).message}`);
  }
}

/**
 * Unregister device from internet server
 */
async function unregisterFromInternetServer() {
  try {
    const response = await fetch(`${internetSyncConfig.serverUrl}/api/sync/unregister-device`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${internetSyncConfig.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        deviceId: localDeviceId
      })
    });
    
    if (response.ok) {
      sendP2PLog(`🌐 ✅ Successfully unregistered from internet server`);
    } else {
      const errorText = await response.text();
      sendP2PLog(`🌐 ⚠️ Unregistration failed: ${response.status} ${errorText}`);
    }
    
  } catch (error) {
    sendP2PLog(`🌐 ⚠️ Unregistration error: ${(error as Error).message}`);
  }
}

/**
 * Set up IPC handlers for P2P sync operations
 */
function setupP2PSyncIpcHandlers() {
  console.log('[p2p-sync.ts] Setting up IPC handlers...');
  
  // Get all discovered peers
  ipcMain.handle('p2p-get-peers', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-peers called, returning', peers.size, 'peers');
    return Array.from(peers.values());
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-peers handler');
  
  // Get status of all active syncs
  ipcMain.handle('p2p-get-sync-status', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-sync-status called, returning', syncStatuses.length, 'statuses');
    return syncStatuses;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-sync-status handler');
  
  // Get mDNS status
  ipcMain.handle('p2p-get-mdns-status', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-mdns-status called, returning:', mdnsStatus);
    return mdnsStatus;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-mdns-status handler');
  
  // Get system ID (device ID) for diagnostics
  ipcMain.handle('p2p-get-system-id', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-system-id called, returning:', localDeviceId);
    return localDeviceId;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-system-id handler');
  
  // NEW: Get server port for diagnostics
  ipcMain.handle('p2p-get-server-port', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-server-port called, returning:', serverPort);
    return serverPort;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-server-port handler');
  
  // NEW: Get CouchDB port specifically (alias for UI database detection)
  ipcMain.handle('get-couchdb-port', () => {
    console.log('[p2p-sync.ts] IPC: get-couchdb-port called, returning:', serverPort);
    return serverPort;
  });
  console.log('[p2p-sync.ts] ✅ Registered get-couchdb-port handler');
  
  // NEW: Get service info for renderer
  ipcMain.handle('p2p-get-service-info', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-service-info called, returning:', lastServiceInfo);
    return lastServiceInfo;
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-service-info handler');
  
  // Debug bonjour module
  ipcMain.handle('debug-bonjour-module', () => {
    console.log('[p2p-sync.ts] IPC: debug-bonjour-module called');
    
    try {
      // Test different import methods
      let alternativeImport = null;
      try {
        alternativeImport = require('bonjour-service');
      } catch (e) {
        // Ignore import errors for now
      }
      
      const debugInfo: any = {
        timestamp: new Date().toISOString(),
        currentImportMethod: 'require("bonjour-service")',
        moduleType: typeof BonjourService,
        moduleString: BonjourService.toString().substring(0, 300),
        moduleKeys: Object.getOwnPropertyNames(BonjourService),
        moduleConstructor: BonjourService.constructor?.name,
        isClass: /^class\s/.test(BonjourService.toString()),
        isFunction: typeof BonjourService === 'function',
        canInstantiate: false,
        instantiationError: null,
        alternativeImportSame: alternativeImport === BonjourService,
        prototypeKeys: BonjourService.prototype ? Object.getOwnPropertyNames(BonjourService.prototype) : null
      };
      
      // Test if we can create an instance
      try {
        const testInstance = new BonjourService();
        debugInfo.canInstantiate = true;
        debugInfo.instanceType = typeof testInstance;
        debugInfo.instanceMethods = Object.getOwnPropertyNames(testInstance);
        
        // Test if instance has publish method
        debugInfo.hasPublish = typeof testInstance.publish === 'function';
        debugInfo.hasFind = typeof testInstance.find === 'function';
        
        // Clean up
        if (testInstance.destroy) {
          testInstance.destroy();
        }
      } catch (instError: any) {
        debugInfo.instantiationError = instError.message;
      }
      
      // Try alternative ways to access the module
      try {
        const altInstance = BonjourService();
        debugInfo.functionCall = {
          success: true,
          type: typeof altInstance,
          methods: Object.getOwnPropertyNames(altInstance)
        };
      } catch (funcError: any) {
        debugInfo.functionCall = {
          success: false,
          error: funcError.message
        };
      }
      
      // Test for default export pattern
      try {
        if (BonjourService.default && typeof BonjourService.default === 'function') {
          const defaultInstance = new BonjourService.default();
          debugInfo.defaultExport = {
            success: true,
            type: typeof defaultInstance,
            methods: Object.getOwnPropertyNames(defaultInstance)
          };
          if (defaultInstance.destroy) defaultInstance.destroy();
        }
      } catch (defError: any) {
        debugInfo.defaultExport = {
          success: false,
          error: defError.message
        };
      }
      
      // Additional module analysis
      debugInfo.hasDefault = 'default' in BonjourService;
      debugInfo.moduleDescriptor = Object.getOwnPropertyDescriptor(BonjourService, 'default');
      
      debugInfo.success = debugInfo.canInstantiate || debugInfo.functionCall?.success || debugInfo.defaultExport?.success;
      
      console.log('[p2p-sync.ts] Debug info:', debugInfo);
      return debugInfo;
      
    } catch (error: any) {
      console.error('[p2p-sync.ts] Debug error:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered debug-bonjour-module handler');
  
  // Internet sync configuration
  ipcMain.handle('p2p-configure-internet-sync', (_event, config) => {
    console.log('[p2p-sync.ts] IPC: p2p-configure-internet-sync called with:', config);
    try {
      configureInternetSync(config);
      return { success: true };
    } catch (error) {
      console.error('Failed to configure internet sync:', error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-configure-internet-sync handler');
  
  // Get internet sync status
  ipcMain.handle('p2p-get-internet-sync-status', () => {
    console.log('[p2p-sync.ts] IPC: p2p-get-internet-sync-status called');
    return {
      enabled: internetSyncConfig.enabled,
      serverUrl: internetSyncConfig.serverUrl,
      registered: internetSyncConfig.registrationInterval !== null
    };
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-get-internet-sync-status handler');
  
  // Start syncing with a peer
  ipcMain.handle('p2p-start-sync', async (_event, peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'both') => {
    console.log('[p2p-sync.ts] IPC: p2p-start-sync called with:', { peerId, dbName, direction });
    try {
      const result = await startSync(peerId, dbName, direction);
      return { success: true, data: result };
    } catch (error) {
      console.error(`Failed to start sync with peer ${peerId} for DB ${dbName}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-start-sync handler');
  
  // Stop syncing with a peer for a specific database
  ipcMain.handle('p2p-stop-sync', async (_event, peerId: string, dbName: string) => {
    console.log('[p2p-sync.ts] IPC: p2p-stop-sync called with:', { peerId, dbName });
    try {
      await stopSync(peerId, dbName);
      return { success: true };
    } catch (error) {
      console.error(`Failed to stop sync with peer ${peerId} for DB ${dbName}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-stop-sync handler');
  
  // Stop all syncs with a peer
  ipcMain.handle('p2p-stop-all-syncs-with-peer', async (_event, peerId: string) => {
    console.log('[p2p-sync.ts] IPC: p2p-stop-all-syncs-with-peer called with:', { peerId });
    try {
      await stopAllSyncsWithPeer(peerId);
      return { success: true };
    } catch (error) {
      console.error(`Failed to stop all syncs with peer ${peerId}:`, error);
      return { success: false, error: String(error) };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-stop-all-syncs-with-peer handler');
  
  // Add mDNS restart handler for recovery
  ipcMain.handle('p2p-restart-mdns', async () => {
    console.log('[p2p-sync.ts] IPC: p2p-restart-mdns called');
    try {
      // Stop current bonjour service if running
      if (bonjour && typeof bonjour.destroy === 'function') {
        console.log('[p2p-sync.ts] Stopping existing bonjour service...');
        bonjour.destroy();
        bonjour = null;
      }
      
      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Restart the discovery
      console.log('[p2p-sync.ts] Restarting mDNS discovery...');
      const restarted = await startBonjourDiscovery();
      
      if (restarted) {
        mdnsStatus = 'running';
        sendP2PLog('✅ mDNS service restarted successfully');
        return { success: true, status: 'running' };
      } else {
        mdnsStatus = 'error';
        sendP2PLog('❌ mDNS restart failed');
        return { success: false, status: 'error', error: 'Restart failed' };
      }
    } catch (error: any) {
      console.error('[p2p-sync.ts] mDNS restart error:', error);
      mdnsStatus = 'error';
      sendP2PLog(`❌ mDNS restart error: ${error.message}`);
      return { success: false, status: 'error', error: error.message };
    }
  });
  console.log('[p2p-sync.ts] ✅ Registered p2p-restart-mdns handler');
  
  // Set up periodic heartbeat to send diagnostic logs
  const heartbeatInterval = setInterval(() => {
    sendP2PLog(`❤️ P2P sync heartbeat - mDNS status: ${mdnsStatus}, peers: ${peers.size}, syncs: ${activeSyncs.size}`);
  }, 30000);
  
  // Cleanup function to clear the interval if needed
  function clearHeartbeat() {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
    }
  }
  
  console.log('[p2p-sync.ts] ✅ All IPC handlers registered successfully');
}

/**
 * Cleanup P2P sync resources on error or shutdown
 */
export async function cleanupP2PSync() {
  console.log('[p2p-sync.ts] Cleaning up P2P sync resources');
  
  // 🚀 CRITICAL FIX: Terminate embedded CouchDB when application exits on **all** platforms & modes
  if (couchdbProcess) {
    try {
      if (process.platform === 'win32') {
        // Use SIGTERM on Windows (graceful) but fallback to SIGKILL if unsupported
        couchdbProcess.kill('SIGTERM');
      } else {
        couchdbProcess.kill('SIGTERM');
      }
      console.log('[p2p-sync.ts] ✅ CouchDB process terminated');
    } catch (error) {
      console.error('[p2p-sync.ts] Error terminating CouchDB process:', error);
    }
    couchdbProcess = null;
  }
  
  // Destroy the Bonjour instance
  if (bonjour) {
    try {
      if (typeof bonjour.destroy === 'function') {
        bonjour.destroy();
        console.log('[p2p-sync.ts] Bonjour instance destroyed');
      }
    } catch (error) {
      console.error('[p2p-sync.ts] Error destroying Bonjour instance:', error);
    }
    bonjour = null;
  }
  
  // Stop internet registration
  stopInternetRegistration();
  
  // Clear other state
  peers.clear();
  activeSyncs.clear();
  syncStatuses.length = 0;
  
  console.log('[p2p-sync.ts] P2P sync resources cleaned up');
  return true;
}

/**
 * Start syncing a database with a peer
 */
async function startSync(peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'both'): Promise<SyncStatus> {
  console.log(`[p2p-sync.ts] Starting sync with peer ${peerId} for DB ${dbName}, direction: ${direction}`);
  
  // Find the peer in our peers map
  const peer = peers.get(peerId);
  if (!peer) {
    const error = `Peer ${peerId} not found`;
    console.error(`[p2p-sync.ts] ${error}`);
    throw new Error(error);
  }
  
  // Check if this is a mobile peer trying to sync locally - mobile can sync via internet proxy
  if ((peer.platform === 'mobile' || peer.platform === 'android' || peer.platform === 'ios') && 
      !process.env.VPS_BASE_URL) {
    const error = 'Cannot sync directly to mobile peers locally - use internet sync via VPS proxy instead';
    console.error(`[p2p-sync.ts] ${error}`);
    sendP2PLog(`⚠️ ${error}`);
    throw new Error(error);
  }
  
  // Generate a unique key for this sync
  const syncKey = `${peerId}:${dbName}`;
  
  // Check if we already have a sync running for this peer and DB
  if (activeSyncs.has(syncKey)) {
    console.log(`[p2p-sync.ts] Sync already exists for ${syncKey}, returning existing status`);
    // Find and return the existing sync status
    const existingStatus = syncStatuses.find(
      s => s.peerId === peerId && s.dbName === dbName
    );
    if (existingStatus) {
      return existingStatus;
    }
  }
  
  try {
    // Create a PouchDB instance for the local database
    const PouchDB = PouchDBConstructor.defaults({
      prefix: dbRootPathRef + path.sep,
      auto_compaction: true,
    });
    const localDb = new PouchDB(dbName);
    
    // Construct the remote database URL
    const remoteDbUrl = `http://${peer.ip}:${peer.port}/${dbName}`;
    console.log(`[p2p-sync.ts] Remote database URL: ${remoteDbUrl}`);
    const remoteDb = new PouchDB(remoteDbUrl);
    
    // Setup sync options based on direction
    const syncOptions: PouchDB.Replication.ReplicateOptions = {
      live: true,
      retry: true,
    };
    
    // Determine replication method based on direction
    let syncHandler;
    switch (direction) {
      case 'push':
        console.log(`[p2p-sync.ts] Setting up one-way push sync to ${remoteDbUrl}`);
        syncHandler = localDb.replicate.to(remoteDb, syncOptions);
        break;
      case 'pull':
        console.log(`[p2p-sync.ts] Setting up one-way pull sync from ${remoteDbUrl}`);
        syncHandler = localDb.replicate.from(remoteDb, syncOptions);
        break;
      case 'both':
      default:
        console.log(`[p2p-sync.ts] Setting up two-way sync with ${remoteDbUrl}`);
        syncHandler = localDb.sync(remoteDb, syncOptions);
        break;
    }
    
    // Create a status object
    const syncStatus: SyncStatus = {
      peerId,
      dbName,
      direction,
      status: 'active',
    };
    
    // Add the status to our array
    const existingStatusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (existingStatusIndex >= 0) {
      syncStatuses[existingStatusIndex] = syncStatus;
    } else {
      syncStatuses.push(syncStatus);
    }
    
    // Store the sync handler with the localDb reference
    activeSyncs.set(syncKey, { 
      syncHandler, 
      localDb,
      remoteDb
    });
    
    // Set up event handlers
    (syncHandler as any).on('change', (info: any) => {
      console.log(`[p2p-sync.ts] Sync change for ${syncKey}:`, info);
      
      // Update the sync status with progress info
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        const progress = syncStatuses[statusIndex].progress || { 
          docsPushed: 0, 
          docsPulled: 0, 
          totalDocs: 0 
        };
        
        // Update progress based on the sync direction and available info
        if (direction === 'push' || direction === 'both') {
          progress.docsPushed += info.docs ? info.docs.length : 0;
        }
        if (direction === 'pull' || direction === 'both') {
          progress.docsPulled += info.docs ? info.docs.length : 0;
        }
        
        syncStatuses[statusIndex].progress = progress;
        syncStatuses[statusIndex].status = 'active';
        
        // Notify the UI of the status update
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('paused', () => {
      console.log(`[p2p-sync.ts] Sync paused for ${syncKey}`);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'paused';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('active', () => {
      console.log(`[p2p-sync.ts] Sync active for ${syncKey}`);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'active';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('error', (err: any) => {
      console.error(`[p2p-sync.ts] Sync error for ${syncKey}:`, err);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'error';
        syncStatuses[statusIndex].error = err.message || String(err);
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    (syncHandler as any).on('complete', (info: any) => {
      console.log(`[p2p-sync.ts] Sync complete for ${syncKey}:`, info);
      
      // Update the status
      const statusIndex = syncStatuses.findIndex(
        s => s.peerId === peerId && s.dbName === dbName
      );
      
      if (statusIndex >= 0) {
        syncStatuses[statusIndex].status = 'complete';
        
        // Notify the UI
        if (mainWindowRef) {
          mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
        }
      }
    });
    
    // Return the sync status
    return syncStatus;
  } catch (error) {
    console.error(`[p2p-sync.ts] Error starting sync for ${syncKey}:`, error);
    
    // Create an error status
    const errorStatus: SyncStatus = {
      peerId,
      dbName,
      direction,
      status: 'error',
      error: error instanceof Error ? error.message : String(error)
    };
    
    // Add to or update the statuses array
    const existingStatusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (existingStatusIndex >= 0) {
      syncStatuses[existingStatusIndex] = errorStatus;
    } else {
      syncStatuses.push(errorStatus);
    }
    
    // Notify the UI
    if (mainWindowRef) {
      mainWindowRef.webContents.send('sync-status-updated', errorStatus);
    }
    
    throw error;
  }
}

/**
 * Stop syncing a database with a peer
 */
async function stopSync(peerId: string, dbName: string): Promise<boolean> {
  console.log(`[p2p-sync.ts] Stopping sync with peer ${peerId} for DB ${dbName}`);
  
  // Generate the sync key
  const syncKey = `${peerId}:${dbName}`;
  
  // Find the sync in our active syncs
  const syncInfo = activeSyncs.get(syncKey);
  if (!syncInfo) {
    console.log(`[p2p-sync.ts] No active sync found for ${syncKey}`);
    return false;
  }
  
  try {
    // Cancel the sync
    if (syncInfo.syncHandler && typeof syncInfo.syncHandler.cancel === 'function') {
      syncInfo.syncHandler.cancel();
    }
    
    // Close the database connections
    if (syncInfo.localDb && typeof syncInfo.localDb.close === 'function') {
      await syncInfo.localDb.close();
    }
    
    if (syncInfo.remoteDb && typeof syncInfo.remoteDb.close === 'function') {
      await syncInfo.remoteDb.close();
    }
    
    // Remove from active syncs
    activeSyncs.delete(syncKey);
    
    // Update the status
    const statusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (statusIndex >= 0) {
      // Remove from the status array
      syncStatuses.splice(statusIndex, 1);
      
      // Notify the UI
      if (mainWindowRef) {
        mainWindowRef.webContents.send('sync-status-updated', { 
          peerId, 
          dbName, 
          status: 'complete',
          direction: 'both' 
        });
      }
    }
    
    console.log(`[p2p-sync.ts] Successfully stopped sync for ${syncKey}`);
    return true;
  } catch (error) {
    console.error(`[p2p-sync.ts] Error stopping sync for ${syncKey}:`, error);
    
    // Still remove from active syncs even if there was an error
    activeSyncs.delete(syncKey);
    
    // Still notify the UI about the error
    const statusIndex = syncStatuses.findIndex(
      s => s.peerId === peerId && s.dbName === dbName
    );
    
    if (statusIndex >= 0) {
      syncStatuses[statusIndex].status = 'error';
      syncStatuses[statusIndex].error = error instanceof Error ? error.message : String(error);
      
      if (mainWindowRef) {
        mainWindowRef.webContents.send('sync-status-updated', syncStatuses[statusIndex]);
      }
    }
    
    return false;
  }
}

/**
 * Stop all syncs with a specific peer
 */
async function stopAllSyncsWithPeer(peerId: string): Promise<boolean> {
  console.log(`[p2p-sync.ts] Stopping all syncs with peer ${peerId}`);
  
  // Find all syncs with this peer
  const peerSyncs = syncStatuses.filter(s => s.peerId === peerId);
  if (peerSyncs.length === 0) {
    console.log(`[p2p-sync.ts] No active syncs found for peer ${peerId}`);
    return true;
  }
  
  let allSucceeded = true;
  
  // Stop each sync
  for (const sync of peerSyncs) {
    try {
      const success = await stopSync(peerId, sync.dbName);
      if (!success) {
        allSucceeded = false;
      }
    } catch (error) {
      console.error(`[p2p-sync.ts] Error stopping sync for peer ${peerId}, DB ${sync.dbName}:`, error);
      allSucceeded = false;
    }
  }
  
  return allSucceeded;
}

/**
 * Shutdown the P2P sync system
 */
export async function shutdownP2PSync() {
  console.log('[p2p-sync.ts] Shutting down P2P sync system');
  
  // Stop all active syncs
  for (const [syncKey, _] of activeSyncs) {
    const [peerId, dbName] = syncKey.split(':');
    try {
      await stopSync(peerId, dbName);
    } catch (error) {
      console.error(`[p2p-sync.ts] Error stopping sync for ${syncKey} during shutdown:`, error);
    }
  }
  
  // Clean up all resources
  return cleanupP2PSync();
}

// -- Helper to get the CouchDB port for other modules
export function getCouchDBPort(): number {
  return serverPort;
}
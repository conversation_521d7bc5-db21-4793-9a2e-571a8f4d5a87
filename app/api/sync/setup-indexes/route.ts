import { NextRequest, NextResponse } from 'next/server';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';


export async function POST(req: NextRequest) {
  try {
    const database = databaseV4.getDatabase();
    const collection = database.collection('sync_devices');

    // Create compound indexes for optimal tenant isolation and performance
    await collection.createIndex(
      { restaurantId: 1, deviceId: 1 },
      { unique: true, background: true }
    );

    await collection.createIndex(
      { restaurantId: 1, deviceType: 1, status: 1, lastSeen: 1 },
      { background: true }
    );

    // Create TTL index to automatically clean up old devices (30 days)
    await collection.createIndex(
      { lastSeen: 1 },
      { expireAfterSeconds: 30 * 24 * 60 * 60, background: true }
    );

    return NextResponse.json({
      message: 'Sync indexes created successfully',
      indexes: [
        'restaurantId_1_deviceId_1 (unique)',
        'restaurantId_1_deviceType_1_status_1_lastSeen_1',
        'lastSeen_1 (TTL: 30 days)'
      ]
    });

  } catch (error) {
    console.error('[API Sync Setup Indexes] Error:', error);
    return NextResponse.json({ 
      error: 'Failed to create indexes',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
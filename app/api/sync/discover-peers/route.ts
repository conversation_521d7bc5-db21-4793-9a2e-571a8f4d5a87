import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth/new-auth-service';
import { databaseV4 } from '@/lib/db/v4/core/db-instance';

// Static export configuration
export { dynamic, revalidate } from '@/lib/api-config';


// Simple rate limiting (in production, use Redis or external service)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX = 30; // 30 requests per minute

function checkRateLimit(identifier: string): boolean {
  const now = Date.now();
  const current = rateLimitMap.get(identifier);
  
  if (!current || now > current.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (current.count >= RATE_LIMIT_MAX) {
    return false;
  }
  
  current.count++;
  return true;
}

export async function GET(req: NextRequest) {
  try {
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authorization token required' }, { status: 401 });
    }

    const token = authHeader.replace('Bearer ', '');
    const decoded = verifyToken(token);
    
    if (!decoded || !decoded.restaurantId) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }

    // Rate limiting per restaurant
    if (!checkRateLimit(decoded.restaurantId)) {
      return NextResponse.json({ 
        error: 'Rate limit exceeded - too many discovery requests' 
      }, { status: 429 });
    }

    const database = databaseV4.getDatabase();
    const collection = database.collection('sync_devices');

    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const peers = await collection.find({
      restaurantId: decoded.restaurantId,
      status: 'active',
      lastSeen: { $gte: fiveMinutesAgo }
    }).toArray();

    const formattedPeers = peers.map(peer => ({
      id: peer.deviceId,
      deviceType: peer.deviceType,
      ipAddress: peer.ipAddress,
      couchdbPort: peer.couchdbPort,
      hostname: `${peer.deviceType}-${peer.deviceId.substring(0, 8)}`,
      platform: peer.deviceType === 'desktop' ? 'desktop' : 'mobile',
      lastSeen: peer.lastSeen,
      registeredAt: peer.registeredAt
    }));

    return NextResponse.json({
      peers: formattedPeers,
      restaurantId: decoded.restaurantId,
      count: formattedPeers.length
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });

  } catch (error) {
    console.error('[API Sync Discover Peers] Error:', error);
    return NextResponse.json({ 
      error: 'Peer discovery failed' 
    }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
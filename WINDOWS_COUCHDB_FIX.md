# 🪟 Windows CouchDB Production Startup Fix

## 🔍 Problem Analysis

The embedded CouchDB server (referred to as "ArchDB server" in the issue) was failing to start on Windows in production Electron builds, while working correctly on macOS. This document outlines the root causes and implemented fixes.

## 🚨 Root Causes Identified

### 1. **ERTS Version Resolution Issues**
- **Problem**: Inconsistent parsing of ERTS version from `start_erl.data` file
- **Impact**: Wrong Erlang runtime directory paths, causing binary not found errors
- **Fix**: Always use authoritative version from `start_erl.data` file

### 2. **Path Resolution Problems**
- **Problem**: Windows paths with spaces breaking batch file execution
- **Impact**: Portable copy mechanism not preserving all necessary files
- **Fix**: Enhanced portable copy logic with proper space handling

### 3. **Environment Variable Mismatches**
- **Problem**: Spawn environment not matching Windows batch file requirements
- **Impact**: Erlang VM failing to initialize with correct settings
- **Fix**: Exact environment variable replication from `couchdb.cmd`

### 4. **Spawn Configuration Issues**
- **Problem**: Direct `erl.exe` spawn with incorrect arguments
- **Impact**: Erlang VM startup failures due to malformed command line
- **Fix**: Proper argument formatting and fallback to batch file

## 🛠️ Implemented Fixes

### Enhanced ERTS Version Detection
```javascript
// Always use the version from start_erl.data as it's authoritative
const startErlContent = fs.readFileSync(startErlPath, 'utf8').trim();
const fileErtsVersion = startErlContent.split(' ')[0].trim();
expectedErtsVersion = fileErtsVersion;
```

### Improved Path Construction
```javascript
// Construct paths using the correct ERTS version
const erlangHomeDir = path.join(actualCouchdbPath, `erts-${expectedErtsVersion}`);
const erlangBinDir = path.join(erlangHomeDir, 'bin');
```

### Exact Environment Variable Replication
```javascript
// Match couchdb.cmd environment exactly
spawnEnv.COUCHDB_BIN_DIR = couchBinDir;
spawnEnv.ROOTDIR = actualCouchdbPath;
spawnEnv.ERTS_VSN = expectedErtsVersion;
spawnEnv.BINDIR = erlangBinDir;
spawnEnv.EMU = 'beam';
spawnEnv.PROGNAME = 'couchdb';
```

### Proper Argument Formatting
```javascript
// Use forward slashes for Erlang paths on Windows
spawnArgs = [
  '-boot', path.join(actualCouchdbPath, 'releases', 'couchdb').replace(/\\/g, '/'),
  '-args_file', path.join(actualCouchdbPath, 'etc', 'vm.args').replace(/\\/g, '/'),
  // ... other args with proper path formatting
];
```

### Batch File Fallback Mechanism
```javascript
// Fallback to batch file if direct erl.exe spawn fails
async function tryWindowsBatchFallback(couchdbPath, configPath) {
  const batchFile = path.join(couchdbPath, 'bin', 'couchdb.cmd');
  const batchArgs = ['-couch_ini', defaultIni, configPath];
  return spawn(batchFile, batchArgs, { cwd: couchdbPath, shell: true });
}
```

## 🧪 Testing & Validation

### Debug Script
```bash
npm run debug:windows-couchdb
```
- Comprehensive Windows environment check
- CouchDB bundle verification
- Erlang runtime validation
- Batch file execution test
- Diagnostic report generation

### Fix Validation Script
```bash
npm run test:windows-couchdb-fix
```
- Simulates production Electron environment
- Tests the fixed startup logic
- Validates HTTP connectivity
- Confirms CouchDB functionality

## 📁 Files Modified

### Core Fix Implementation
- `electron/src/couchdb-server.ts` - Main Windows startup logic fixes

### New Debug Tools
- `scripts/debug-windows-couchdb.js` - Windows environment debugging
- `scripts/test-windows-couchdb-fix.js` - Fix validation testing

### Documentation
- `WINDOWS_COUCHDB_FIX.md` - This comprehensive fix documentation

## 🚀 Deployment Instructions

### For Development Testing
1. **Run debug script**: `npm run debug:windows-couchdb`
2. **Validate fixes**: `npm run test:windows-couchdb-fix`
3. **Test in Electron**: `npm run electron:start`

### For Production Builds
1. **Ensure CouchDB bundle**: Check `electron/resources/couchdb-windows/` exists
2. **Build Electron app**: `cd electron && npm run electron:build:win`
3. **Test production build**: Install and run the generated `.exe`

## 🔧 Troubleshooting

### If CouchDB Still Fails to Start

1. **Check debug log**: Look for `windows-couchdb-debug.log` in app data directory
2. **Run diagnostic**: `npm run debug:windows-couchdb`
3. **Verify bundle**: Ensure all files in `electron/resources/couchdb-windows/` are present
4. **Check permissions**: Run as administrator if needed
5. **Antivirus**: Whitelist the application directory

### Common Error Patterns

- **"ENOENT" errors**: Missing binaries or incorrect paths
- **"EACCES" errors**: Permission issues, try running as administrator
- **Exit code 1**: Configuration errors, check INI files
- **Exit code 2**: Missing dependencies, verify bundle completeness
- **Exit code 3**: Erlang VM startup failure, check ERTS version

## 📊 Expected Outcomes

After applying these fixes:

✅ **Windows Production Builds**: CouchDB starts reliably in packaged Electron apps
✅ **Cross-Platform Consistency**: Same behavior on Windows and macOS
✅ **Error Handling**: Better error messages and fallback mechanisms
✅ **Debugging**: Comprehensive tools for troubleshooting issues
✅ **Maintainability**: Clear separation of Windows-specific logic

## 🔄 Future Improvements

1. **Automated Testing**: CI/CD pipeline for Windows CouchDB testing
2. **Performance Optimization**: Faster startup times through caching
3. **Error Recovery**: Automatic restart mechanisms for failed instances
4. **Monitoring**: Health checks and status reporting
5. **Configuration**: Dynamic port selection and conflict resolution

## 📞 Support

If issues persist after applying these fixes:

1. Generate diagnostic report: `npm run debug:windows-couchdb`
2. Check application logs in `%APPDATA%/Bistro/pouchdb-data/`
3. Review Windows Event Viewer for system-level errors
4. Test with antivirus temporarily disabled
5. Verify Windows version compatibility (Windows 10+ recommended)

---

**Note**: These fixes specifically address Windows production deployment issues. The development environment should continue to work as before, with enhanced debugging capabilities.

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useStaticNavigation } from '@/lib/utils/navigation';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Crown, 
  Shield, 
  User,
  Wifi,
  WifiOff,
  RefreshCw,
  UserPlus,
  ArrowLeft
} from 'lucide-react';
import { useMultiUserAuth } from '@/lib/hooks/use-multi-user-auth';
import { UserSession } from '@/lib/auth/multi-user-session-manager';
import { toast } from 'sonner';

interface UserSwitchDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserSwitchDialog({ open, onOpenChange }: UserSwitchDialogProps) {
  const {
    currentUser,
    availableUsers,
    isOfflineMode,
    login,
    switchToUser,
    logout,
  } = useMultiUserAuth();



  const router = useRouter();
  const { navigate } = useStaticNavigation();
  const [selectedUser, setSelectedUser] = useState<UserSession | null>(null);
  const [password, setPassword] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);
  const [addUserForm, setAddUserForm] = useState({
    identifier: '',
    password: '',
    isStaffLogin: false
  });
  const [isAddingUser, setIsAddingUser] = useState(false);

  // Get other users (excluding current user)
  const otherUsers = availableUsers.filter(session => session.user.id !== currentUser?.id);

  // Helper functions
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'manager':
        return <Shield className="h-4 w-4 text-green-500" />;
      default:
        return <User className="h-4 w-4 text-gray-500" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'manager':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Handle user selection
  const handleUserSelect = async (user: UserSession) => {
    setSelectedUser(user);
    setPassword('');
    
    // For offline users, switch immediately without password verification
    if (!user.isOnline || isOfflineMode) {
      const success = await switchToUser(user.user.id);
      if (success) {
        toast.success(`🔄 Switched to ${user.user.name}`);
        onOpenChange(false);
      } else {
        toast.error('❌ Failed to switch user');
      }
    }
    // For online users in online mode, show password verification
    else if (user.isOnline && !isOfflineMode) {
      // The password verification screen will be shown automatically
      // because selectedUser is set
    }
  };

  // Handle password verification and user switching
  const handleVerifyAndSwitch = async () => {
    if (!selectedUser) return;

    setIsVerifying(true);
    try {
      // For online sessions, verify password by attempting login
      const identifier = selectedUser.user.email || selectedUser.user.name;
      const success = await login({
        identifier,
        password,
        isStaffLogin: !selectedUser.user.email // Use staff login if no email
      });

      if (success) {
        toast.success(`🔄 Switched to ${selectedUser.user.name}`);
        onOpenChange(false);
        setSelectedUser(null);
        setPassword('');
      } else {
        toast.error('❌ Invalid password');
        setPassword('');
      }
    } catch (error) {
      console.error('❌ Error switching user:', error);
      toast.error('❌ Failed to switch user');
      setPassword('');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle showing add user form
  const handleShowAddUser = () => {
    setShowAddUser(true);
    setAddUserForm({
      identifier: '',
      password: '',
      isStaffLogin: false
    });
  };

  // Handle adding new user directly in the dialog
  const handleAddUser = async () => {
    if (!addUserForm.identifier.trim() || !addUserForm.password.trim()) {
      toast.error('❌ Please enter both username/email and password');
      return;
    }

    setIsAddingUser(true);
    try {
      // Use the multi-user auth hook to add a new user session
      const success = await login({
        identifier: addUserForm.identifier.trim(),
        password: addUserForm.password.trim(),
        isStaffLogin: addUserForm.isStaffLogin
      });

      if (success) {
        toast.success(`✅ Successfully added and switched to new user!`);
        onOpenChange(false);
        // Reset form
        setAddUserForm({
          identifier: '',
          password: '',
          isStaffLogin: false
        });
        setShowAddUser(false);
      } else {
        toast.error('❌ Failed to add user. Please check your credentials and internet connection.');
      }
    } catch (error) {
      console.error('❌ Error adding user:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add user. Please try again.';
      toast.error(`❌ ${errorMessage}`);
    } finally {
      setIsAddingUser(false);
    }
  };

  // Handle going to auth page for new user (fallback)
  const handleGoToAuth = () => {
    onOpenChange(false);
    
    // Store current user info in sessionStorage for restoration
    if (currentUser) {
      sessionStorage.setItem('temp_switch_user_context', JSON.stringify({
        action: 'add_user',
        currentUserId: currentUser.id,
        timestamp: Date.now()
      }));
    }
    
    // Navigate to auth page with add user parameter
    navigate('auth?mode=add_user');
    
    // Show toast to inform user
    toast.info('🔄 Redirecting to login page to add new user...');
  };

  // Handle back to user selection
  const handleBack = () => {
    setSelectedUser(null);
    setPassword('');
    setShowAddUser(false);
    setAddUserForm({
      identifier: '',
      password: '',
      isStaffLogin: false
    });
  };

  // If no other users available, show add user option
  if (otherUsers.length === 0) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              👥 Switch User
            </DialogTitle>
            <DialogDescription>
              No other users are available on this device. You can add a new user by logging them in.
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col gap-3 py-4">
            <Button 
              onClick={handleShowAddUser}
              className="flex items-center gap-2"
            >
              <UserPlus className="h-4 w-4" />
              Add New User
            </Button>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // If user is selected, show password verification
  if (selectedUser) {
    const needsPassword = selectedUser.isOnline && !isOfflineMode;
    
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="h-6 w-6 p-0 mr-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              🔐 Verify Identity
            </DialogTitle>
            <DialogDescription>
              {needsPassword 
                ? `Enter password for ${selectedUser.user.name} to switch users.`
                : `Switch to ${selectedUser.user.name}? (Offline mode - no password required)`
              }
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Selected user info */}
            <div className="flex items-center gap-3 p-3 bg-accent/50 rounded-lg">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="text-sm">
                  {getInitials(selectedUser.user.name)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="font-medium">{selectedUser.user.name}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  {getRoleIcon(selectedUser.user.role)}
                  <span className="capitalize">{selectedUser.user.role}</span>
                  {selectedUser.user.email && (
                    <span>• {selectedUser.user.email}</span>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                {selectedUser.isOnline ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-orange-500" />
                )}
                <Badge className={getRoleBadgeColor(selectedUser.user.role)}>
                  {selectedUser.user.role}
                </Badge>
              </div>
            </div>

            {/* Password input (only if needed) */}
            {needsPassword && (
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && password) {
                      handleVerifyAndSwitch();
                    }
                  }}
                  autoFocus
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleBack} disabled={isVerifying}>
              Back
            </Button>
            <Button 
              onClick={handleVerifyAndSwitch}
              disabled={isVerifying || (needsPassword && !password)}
            >
              {isVerifying ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  🔄 Switch User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // Show add user form
  if (showAddUser) {
    const isEmailFormat = (identifier: string) => identifier.includes('@');
    
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="h-6 w-6 p-0 mr-2"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <UserPlus className="h-4 w-4" />
              Add New User
            </DialogTitle>
            <DialogDescription>
              Enter credentials for the new user you want to add to this device.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="add-identifier">Username or Email</Label>
              <Input
                id="add-identifier"
                type="text"
                placeholder="Enter username or email"
                value={addUserForm.identifier}
                onChange={(e) => {
                  const value = e.target.value;
                  setAddUserForm(prev => ({ 
                    ...prev, 
                    identifier: value,
                    isStaffLogin: !isEmailFormat(value) // Auto-detect staff vs owner
                  }));
                }}
                disabled={isAddingUser}
                autoFocus
              />
              <p className="text-xs text-muted-foreground">
                Use email for owners, username for staff
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="add-password">Password</Label>
              <Input
                id="add-password"
                type="password"
                placeholder="Enter password"
                value={addUserForm.password}
                onChange={(e) => setAddUserForm(prev => ({ ...prev, password: e.target.value }))}
                disabled={isAddingUser}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && addUserForm.identifier && addUserForm.password) {
                    handleAddUser();
                  }
                }}
              />
            </div>

            {/* User type indicator */}
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              {addUserForm.identifier && (
                <>
                  {getRoleIcon(isEmailFormat(addUserForm.identifier) ? 'owner' : 'staff')}
                  <span>
                    Will be added as: {isEmailFormat(addUserForm.identifier) ? 'Owner' : 'Staff'}
                  </span>
                </>
              )}
            </div>
          </div>

          <DialogFooter className="flex-col gap-2">
            <Button 
              onClick={handleAddUser}
              disabled={isAddingUser || !addUserForm.identifier.trim() || !addUserForm.password.trim()}
              className="w-full"
            >
              {isAddingUser ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Adding User...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User
                </>
              )}
            </Button>
            
            <div className="flex gap-2 w-full">
              <Button variant="outline" onClick={handleBack} disabled={isAddingUser} className="flex-1">
                Back
              </Button>
              <Button 
                variant="outline" 
                onClick={handleGoToAuth} 
                disabled={isAddingUser}
                className="flex-1 text-xs"
              >
                Use Auth Page
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  // Show user selection
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            👥 Switch User
          </DialogTitle>
          <DialogDescription>
            Select a user to switch to. You'll need to verify your identity.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-2 py-4 max-h-80 overflow-y-auto">
          {otherUsers.map((session) => (
            <Button
              key={session.id}
              variant="ghost"
              className="w-full h-auto p-3 justify-start"
              onClick={() => handleUserSelect(session)}
            >
              <div className="flex items-center gap-3 w-full">
                <Avatar className="h-10 w-10">
                  <AvatarFallback className="text-sm">
                    {getInitials(session.user.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-left">
                  <div className="font-medium">{session.user.name}</div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    {getRoleIcon(session.user.role)}
                    <span className="capitalize">{session.user.role}</span>
                    {session.user.email && (
                      <span>• {session.user.email}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {session.isOnline ? (
                    <Wifi className="h-4 w-4 text-green-500" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-orange-500" />
                  )}
                  <Badge className={getRoleBadgeColor(session.user.role)}>
                    {session.user.role}
                  </Badge>
                </div>
              </div>
            </Button>
          ))}
        </div>

        <DialogFooter className="flex-col gap-2">
          <Button 
            variant="outline" 
            onClick={handleShowAddUser}
            className="w-full"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add New User
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
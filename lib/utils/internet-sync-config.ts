import { internetSyncService } from '@/lib/services/internet-sync';
import { internetDiscoveryService } from '@/lib/services/internet-discovery';

interface InternetSyncSetup {
  vpsBaseUrl: string;
  authToken: string;
  refreshToken?: string;
  deviceId: string;
  deviceType: 'desktop' | 'mobile';
  ipAddress?: string;
  couchdbPort?: number;
}

export async function configureInternetSync(setup: InternetSyncSetup): Promise<boolean> {
  try {
    // Get VPS URL from environment or use provided
    const vpsBaseUrl = process.env.VPS_BASE_URL || setup.vpsBaseUrl;
    
    if (!vpsBaseUrl) {
      throw new Error('VPS_BASE_URL not configured');
    }

    // Configure internet sync service
    internetSyncService.configure({
      vpsBaseUrl,
      authToken: setup.authToken,
      refreshToken: setup.refreshToken,
      deviceId: setup.deviceId
    });

    // Configure discovery service
    internetDiscoveryService.configure({
      vpsBaseUrl,
      authToken: setup.authToken,
      deviceId: setup.deviceId,
      deviceType: setup.deviceType
    });

    // Register device if not mobile (mobile devices don't host servers)
    if (setup.deviceType === 'desktop' && setup.ipAddress && setup.couchdbPort) {
      await internetDiscoveryService.registerDevice({
        deviceId: setup.deviceId,
        deviceType: setup.deviceType,
        ipAddress: setup.ipAddress,
        couchdbPort: setup.couchdbPort
      });
    } else if (setup.deviceType === 'mobile') {
      await internetDiscoveryService.registerDevice({
        deviceId: setup.deviceId,
        deviceType: setup.deviceType,
        ipAddress: '0.0.0.0' // Mobile devices don't have fixed IPs
      });
    }

    console.log('✅ Internet sync configured successfully');
    return true;
  } catch (error) {
    console.error('❌ Failed to configure internet sync:', error);
    return false;
  }
}

export function getInternetSyncStatus() {
  return {
    isConfigured: internetSyncService.isConfigured(),
    isRegistered: internetDiscoveryService.isDeviceRegistered(),
    syncStatus: internetSyncService.getStatus(),
    currentPeer: internetSyncService.getCurrentPeer()
  };
}

export async function startInternetSyncWithBestPeer(): Promise<boolean> {
  try {
    const peers = await internetDiscoveryService.discoverPeers();
    
    if (peers.length === 0) {
      console.log('ℹ️ No internet peers available for sync');
      return false;
    }

    // Find the best peer (desktop with most recent activity)
    const desktopPeers = peers.filter(p => p.deviceType === 'desktop');
    if (desktopPeers.length === 0) {
      console.log('ℹ️ No desktop peers available for internet sync');
      return false;
    }

    const bestPeer = desktopPeers.sort((a, b) => 
      new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime()
    )[0];

    console.log(`🌐 Starting internet sync with best peer: ${bestPeer.hostname}`);
    return await internetSyncService.startSync(bestPeer);
  } catch (error) {
    console.error('❌ Failed to start internet sync:', error);
    return false;
  }
}